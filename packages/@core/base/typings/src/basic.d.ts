import type { ComputedRef, Ref } from 'vue';

interface BasicOption {
  label: string;
  value: string;
}

type SelectOption = BasicOption;

type TabOption = BasicOption;

interface BasicUserInfo {
  /**
   * 头像
   */
  avatar: string;
  /**
   * 用户昵称
   */
  realName?: string;
  /**
   * 用户角色
   */
  roles?: string[];
  /**
   * 用户id
   */
  userId: string;
  /**
   * 用户名
   */
  username: string;
}

/**
 * UserInfoDTO，完整用户信息
 */
interface UserAllInfo {
  [property: string]: any;
  // 按钮权限列表
  buttonList: ButtonInfo[];
  // 应用权限列表
  moduleList: AppInfo[];
  // 用户详细信息
  userInfo: UserInfo;
}

/**
 * ButtonInfo，按钮实体
 */
interface ButtonInfo {
  [property: string]: any;
  // 编码
  code?: string;
  // 描述
  description?: string;
  // 有效标志(0-禁用，1-启用)
  enabled?: number;
  // 主键
  id?: number;
  // 关联功能id
  moduleId?: number;
  // 名称
  name?: string;
  // 扩展属性
  propertyJson?: string;
  // 排序
  sortCode?: number;
}

/**
 * AppInfo，应用权限列表
 */
interface AppInfo {
  [property: string]: any;
  // 应用编码
  code?: string;
  // 描述
  description?: string;
  // 是否启用
  enabled?: number;
  // 图标
  icon?: string;
  // 主键
  id?: number;
  // 是否主系统
  isMain?: number;
  // 模块列表
  moduleList?: ModuleInfo[];
  // 应用名称
  name?: string;
  // 排序
  sortCode?: number;
  // 应用类型:PLATFORM=平台应用,CUSTOMER=客户应用
  type?: string;
  // 应用地址
  url?: string;
}

/**
 * ModuleInfo，模块实体
 */
interface ModuleInfo {
  [property: string]: any;
  // 系统CODE
  appCode?: string;
  // 按钮列表
  buttonList?: ButtonInfo[];
  // 分类
  category?: string;
  children?: ModuleInfo[];
  // 编码
  code?: string;
  // 组件
  component?: string;
  // 描述
  description?: string;
  // 有效标志(0-禁用，1-启用)
  enabled?: number;
  // 菜单图标
  icon?: string;
  // 主键
  id?: number;
  // 按钮权限
  isButtonAuth?: number;
  // 列表权限
  isColumnAuth?: number;
  // 数据权限
  isDataAuth?: number;
  // 表单权限
  isFormAuth?: number;
  isLeaf?: boolean;
  // 层级
  level?: number;
  // 链接目标
  linkTarget?: LinkTarget;
  // 关联功能id
  moduleId?: number;
  // 名称
  name?: string;
  // 上级id
  parentId?: number;
  // 上级路径
  parentPath?: string;
  // 扩展属性
  propertyJson?: string;
  // 排序
  sortCode?: number;
  // 类型(1-目录 2-页面 3-外链)
  type?: string;
  // 路由地址
  urlAddress?: string;
}

/**
 * 链接目标
 */
enum LinkTarget {
  // 新窗口打开
  Blank = '_blank',
  // 当前窗口打开
  Self = '_self',
}

/**
 * UserDetailDTO，用户信息
 */
interface UserInfo extends BasicUserInfo {
  [property: string]: any;
  // 生日
  birthday?: Date;
  // 编码
  code?: string;
  // 邮箱
  email?: string;
  // 过期日期；NULL=永不过期
  expiryDate?: Date;
  // 性别
  gender?: string;
  // 分组主键
  groupIds?: number[];
  // 分组名称
  groupNames?: string[];
  // 头像
  headIcon?: string;
  // 主键
  id?: number;
  // 身份ID
  identityId?: string;
  // 身份名称
  identityName?: string;
  // 身份类型
  identityType?: string;
  // 是否管理员
  isAdmin?: number;
  // 直属上级ID
  managerId?: number;
  // 组织主键（主）
  organId?: number;
  // 组织主键列表
  organIds?: number[];
  // 组织名称列表
  organNames?: string[];
  // 密码
  password?: string;
  // 电话
  phone?: string;
  // 岗位主键
  postIds?: number[];
  // 岗位名称
  postNames?: string[];
  // 姓名
  realName?: string;
  // 角色主键
  roleIds?: number[];
  // 角色名称
  roleNames?: string[];
  // 状态：-1=禁用，0=锁定，1=启用
  status?: number;
  // 租户主键
  tenantId?: string;
  // 账户名
  userName?: string;
  // 用户类型
  userType?: string;
}

type ClassType = Array<object | string> | object | string;

declare type ElRef<T extends HTMLElement = HTMLDivElement> = Nullable<T>;

declare type Fn = (...args: any[]) => any;

declare type RefType<T> = null | T;

declare type ComponentRef<T extends HTMLElement = HTMLDivElement> = ComponentElRef<T> | null;

type DynamicProps<T> = {
  [P in keyof T]: ComputedRef<T[P]> | Ref<T[P]> | T[P];
};
declare type LabelValueOptions = {
  [key: string]: boolean | number | string;
  label: string;
  value: any;
}[];

declare type EmitType = (event: string, ...args: any[]) => void;

declare type TargetContext = '_blank' | '_self';

export type {
  AppInfo,
  BasicOption,
  BasicUserInfo,
  ButtonInfo,
  ClassType,
  ComponentRef,
  DynamicProps,
  ElRef,
  EmitType,
  Fn,
  LabelValueOptions,
  ModuleInfo,
  RefType,
  SelectOption,
  TabOption,
  TargetContext,
  UserAllInfo,
  UserInfo,
};
