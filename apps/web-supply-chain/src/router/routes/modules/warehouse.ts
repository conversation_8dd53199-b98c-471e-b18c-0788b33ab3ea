import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      title: '仓储管理',
      order: 5,
    },
    name: 'WarehouseManage',
    path: '/warehouse-manage',
    children: [
      {
        name: 'Info',
        path: 'info',
        component: () => import('#/views/warehouse-manage/info/index.vue'),
        meta: {
          // title: $t('page.projectManagement.initiation'),
          title: '仓库信息管理',
        },
      },
      {
        name: 'ReceiptDocument',
        path: 'receipt-document',
        component: () => import('#/views/warehouse-manage/receipt-document/index.vue'),
        meta: {
          // title: $t('page.projectManagement.initiation'),
          title: '入库单据管理',
        },
      },
      {
        name: 'OutboundDocument',
        path: 'outbound-document',
        component: () => import('#/views/warehouse-manage/outbound-document/index.vue'),
        meta: {
          // title: $t('page.projectManagement.initiation'),
          title: '出库单据管理',
        },
      },
      {
        name: 'transfer',
        path: 'transfer',
        component: () => import('#/views/warehouse-manage/transfer/index.vue'),
        meta: {
          title: '提货申请管理',
        },
      },
      {
        name: 'InventoryInfo',
        path: 'inventory-info',
        component: () => import('#/views/warehouse-manage/inventory-info/index.vue'),
        meta: {
          title: '库存信息查询',
        },
      },
      {
        name: 'InventoryHistory',
        path: 'inventory-history',
        component: () => import('#/views/warehouse-manage/inventory-info/history-index.vue'),
        meta: {
          title: '库存历史查询',
        },
      },
    ],
  },
];

export default routes;
