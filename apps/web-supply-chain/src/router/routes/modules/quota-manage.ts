import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      title: '额度管理',
      order: 12,
    },
    name: 'quota-manage',
    path: '/quota-manage',
    children: [
      {
        name: 'company-limit',
        path: 'company-limit',
        component: () => import('#/views/quota-manage/company-limit/index.vue'),
        meta: {
          title: '企业额度管理',
        },
      },
      {
        name: 'company-list',
        path: 'company-list',
        component: () => import('#/views/quota-manage/company-list/index.vue'),
        meta: {
          title: '企业额度明细',
        },
      },
      {
        name: 'project-list',
        path: 'project-list',
        component: () => import('#/views/quota-manage/project-list/index.vue'),
        meta: {
          title: '项目额度明细',
        },
      },
    ],
  },
];

export default routes;
