import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      title: '资产分类管理',
      order: 15,
    },
    name: 'asset-classification',
    path: '/asset-classification',
    children: [
      {
        name: 'asset-list',
        path: 'asset-list',
        component: () => import('#/views/asset-classification/asset-list/index.vue'),
        meta: {
          title: '资产分类管理',
        },
      },
    ],
  },
];

export default routes;
