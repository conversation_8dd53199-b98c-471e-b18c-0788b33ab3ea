import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: '',
      order: 3,
      title: $t('page.order-manage.title'),
    },
    name: 'order-manage',
    path: '/order-manage',
    children: [
      {
        name: 'purchase',
        path: '/order-manage/purchase',
        component: () => import('#/views/order-manage/purchase/index.vue'),
        meta: {
          icon: '',
          title: $t('page.order-manage.purchase'),
        },
      },
      {
        name: 'sales',
        path: '/order-manage/sales',
        component: () => import('#/views/order-manage/sales/index.vue'),
        meta: {
          icon: '',
          title: '销售订单管理',
        },
      },
      {
        name: 'purchase-return',
        path: '/order-manage/purchase-return',
        component: () => import('#/views/order-manage/purchase-return/index.vue'),
        meta: {
          icon: '',
          title: '采购退货单管理',
        },
      },
      {
        name: 'sales-return',
        path: '/order-manage/sales-return',
        component: () => import('#/views/order-manage/sales-return/index.vue'),
        meta: {
          icon: '',
          title: '销售退货单管理',
        },
      },
    ],
  },
];

export default routes;
