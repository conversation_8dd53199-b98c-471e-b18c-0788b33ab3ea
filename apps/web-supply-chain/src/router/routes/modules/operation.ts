import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: '',
      order: 16,
      title: $t('page.operation.title'),
    },
    name: 'Operation',
    path: '/operation',
    children: [
      {
        meta: {
          icon: '',
          order: -1,
          title: $t('page.operation.inspection.title'),
        },
        name: 'OperationInspection',
        path: '/operation/inspection',
        children: [
          {
            name: 'OperationInspectionManage',
            path: '/operation/inspection/manage',
            component: () => import('#/views/operation/inspection/manage/index.vue'),
            meta: {
              icon: '',
              title: $t('page.operation.inspection.manage'),
            },
          },
        ],
      },
      {
        meta: {
          icon: '',
          order: -1,
          // title: $t('page.operation.inspection.title'),
          title: '企业风险监测',
        },
        name: 'OperationRiskMonitor',
        path: '/operation/risk-monitor',
        children: [
          {
            name: 'OperationRiskMonitorList',
            path: '/operation/risk-monitor/monitor-list',
            component: () => import('#/views/operation/risk-monitor/monitor-list.vue'),
            meta: {
              icon: '',
              // title: $t('page.operation.inspection.manage'),
              title: '监测名单管理',
            },
          },
          {
            name: 'OperationRiskMonitorDynamic',
            path: '/operation/risk-monitor/dynamic',
            component: () => import('#/views/operation/risk-monitor/dynamic/index.vue'),
            meta: {
              icon: '',
              // title: $t('page.operation.inspection.manage'),
              title: '监测动态管理',
            },
          },
        ],
      },
      {
        meta: {
          icon: '',
          order: -1,
          // title: $t('page.operation.inspection.title'),
          title: '运营监督',
        },
        name: 'OperationSupervision',
        path: '/operation/supervision',
        children: [
          {
            name: 'OperationSupervisionIndex',
            path: '/operation/supervision/index',
            component: () => import('#/views/operation/supervision/index.vue'),
            meta: {
              icon: '',
              // title: $t('page.operation.inspection.manage'),
              title: '运营监督',
            },
          },
          {
            name: 'OperationSupervisionEvaluate',
            path: '/operation/supervision/evaluate',
            component: () => import('#/views/operation/evaluate/index.vue'),
            meta: {
              icon: '',
              // title: $t('page.operation.inspection.manage'),
              title: '项目后评价',
            },
          },
        ],
      },
    ],
  },
];

export default routes;
