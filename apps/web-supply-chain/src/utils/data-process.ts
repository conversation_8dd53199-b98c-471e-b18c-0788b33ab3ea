//  @/utils/data-process.ts
/**
 * 根据字典值获取字典标签
 * @param dictList 字典列表
 * @param value 字典值
 * @returns 字典标签或原始值
 */
export const getDictLabel = (dictList: any[], value: string | number): string => {
  if (value === undefined || value === null) return '';

  const item = dictList.find((dict) => {
    // 使用严格相等前先转换为相同类型
    return String(dict.value) === String(value);
  });
  return item ? item.label : String(value);
};

/**
 * 处理多选值显示的函数（支持数组和逗号分隔字符串）
 * @param options 选项列表
 * @param values 值（数组或逗号分隔字符串）
 * @returns 格式化后的标签字符串
 */
export const getMultiDictLabels = (options: any[], values: any[] | string): string => {
  // 处理空值情况
  if (!values || (Array.isArray(values) && values.length === 0) || (typeof values === 'string' && values.trim() === ''))
    return '-';

  let valueArray: string[] = [];

  // 如果是字符串，按逗号分割
  if (typeof values === 'string') {
    valueArray = values.split(',').filter((item) => item.trim() !== '');
  }
  // 如果是数组，直接使用
  else if (Array.isArray(values)) {
    valueArray = values.map(String); // 确保都是字符串
  }

  // 如果处理后仍然为空
  if (valueArray.length === 0) return '-';

  return valueArray
    .map((value) => {
      const option = options.find((opt) => String(opt.value) === value.trim());
      return option ? option.label : value;
    })
    .join(', ');
};
