import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';
import type { ProjectPageParams, ProjectBaseInfo, MeetingInfo } from '#/api';

// 项目评审分页查询
export async function projectReviewPageApi(params: PageListParams) {
  return requestClient.get<ProjectPageParams[]>('/scm/project/review/page', { params });
}

// 项目评审详情查询
export async function projectReviewDetailApi(reviewNodeId: number) {
  return requestClient.get<ProjectBaseInfo>(`/scm/project/review/detail/${reviewNodeId}`);
}

// 项目评审变更
export async function projectReviewEditApi(data: ProjectBaseInfo) {
  return requestClient.post<ProjectBaseInfo>('/scm/project/review/edit', data);
}

// 项目信息作废
export async function projectReviewCancelApi(id: number) {
  return requestClient.post(`/scm/project/review/cancel/${id}`);
}

// 项目信息提交
export async function projectReviewSubmitApi(data: ProjectBaseInfo) {
  return requestClient.post<ProjectBaseInfo>('/scm/project/review/submit', data);
}

// 项目条件查询
export async function projectReviewListApi(params: PageListParams) {
  return requestClient.get<ProjectPageParams[]>('/scm/project/review/list', { params });
}

// 项目评审上传会议纪要
export async function projectReviewUploadSummaryApi(data: MeetingInfo) {
  return requestClient.post<MeetingInfo>('/scm/project/review/upload/summary', data);
}
