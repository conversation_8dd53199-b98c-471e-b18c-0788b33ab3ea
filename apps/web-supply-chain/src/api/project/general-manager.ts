import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';
import type { ProjectPageParams, ProjectBaseInfo, MeetingInfo } from '#/api';

// 总经办申请分页查询
export async function projectGeneralPageApi(params: PageListParams) {
  return requestClient.get<ProjectPageParams[]>('/scm/project/generalManage/page', { params });
}

// 总经办编辑
export async function projectGeneralEditApi(data: ProjectBaseInfo) {
  return requestClient.post<ProjectBaseInfo>('/scm/project/generalManage/edit', data);
}

// 总经办详情查询
export async function projectGeneralDetailApi(code: number) {
  return requestClient.get<ProjectBaseInfo>(`/scm/project/generalManage/detail/${code}`);
}

// 项目信息删除
export async function projectGeneralDeleteApi(id: number) {
  return requestClient.post(`/scm/project/generalManage/delete/${id}`);
}

// 项目信息作废
export async function projectGeneralCancelApi(id: number) {
  return requestClient.post(`/scm/project/generalManage/cancel/${id}`);
}

// 项目信息提交
export async function projectGeneralSubmitApi(data: ProjectBaseInfo) {
  return requestClient.post<ProjectBaseInfo>('/scm/project/generalManage/submit', data);
}

// 项目条件查询
export async function projectGeneralListApi(params: PageListParams) {
  return requestClient.get<ProjectPageParams[]>('/scm/project/generalManage/list', { params });
}

// 总经办上传会议纪要
export async function projectGeneralUploadSummaryApi(data: MeetingInfo) {
  return requestClient.post<MeetingInfo>('/scm/project/generalManage/upload/summary', data);
}
