import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface WarehousePageParams extends BaseDataParams {
  /**
   * 仓库编号
   */
  warehouseCode: string;
  /**
   * 仓库名称
   */
  warehouseName: string;
  /**
   * 所属公司ID
   */
  warehouseCompanyId: string;
  /**
   * 当前页
   */
  current?: string;
  /**
   * 每页的数量
   */
  size?: string;
  /**
   * 正排序规则
   */
  ascs?: string;
  /**
   * 倒排序规则
   */
  descs?: string;
  /**
   * 版本号
   */
  version?: string;
  /**
   * 所属公司名称
   */
  warehouseCompanyName?: string;
  /**
   * 仓库公司统一社会信用代码
   */
  warehouseCompanyCode?: string;
  /**
   * 仓库类型(普通仓库, 冷藏仓库等)
   */
  warehouseType?: string;
  /**
   * 省份
   */
  province?: string;
  /**
   * 城市
   */
  city?: string;
  /**
   * 区县
   */
  district?: string;
  /**
   * 详细地址
   */
  detailAddress?: string;
  /**
   * 是否启用仓位管理(
   * 1: 是,
   * 0: 否
   */
  isLocationManaged?: string;
  /**
   * 状态(ACTIVE: 启用, INACTIVE: 停用)
   */
  status?: string;
  /**
   * 备注
   */
  remarks?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 创建人
   */
  createBy?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 更新人
   */
  updateBy?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 逻辑删除
   */
  deleteFlag?: string;
}

export interface AddWarehouse extends BaseDataParams {
  /**
   * 主键ID
   */
  id?: number;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人ID
   */
  createBy?: number;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 更新人ID
   */
  updateBy?: number;
  /**
   * 删除标志 (0:未删除, 1:已删除)
   */
  deleteFlag?: boolean;
  /**
   * 版本号
   */
  version?: number;
  /**
   * 仓库编号
   */
  warehouseCode: string;
  /**
   * 仓库名称
   */
  warehouseName: string;
  /**
   * 所属公司ID
   */
  warehouseCompanyId: number;
  /**
   * 所属公司名称
   */
  warehouseCompanyName?: string;
  /**
   * 仓库公司统一社会信用代码
   */
  warehouseCompanyCode?: string;
  /**
   * 仓库类型(普通仓库, 冷藏仓库等)
   */
  warehouseType?: string;
  /**
   * 省份
   */
  province?: string;
  /**
   * 城市
   */
  city?: string;
  /**
   * 区县
   */
  district?: string;
  /**
   * 详细地址
   */
  detailAddress?: string;
  /**
   * 状态(ACTIVE:启用, INACTIVE:停用)
   */
  status?: string;
  /**
   * 备注
   */
  remarks?: string;
}

// 仓库信息分页查询
export async function getWarehousePageApi(params: PageListParams) {
  return requestClient.get<WarehousePageParams[]>('/scm/warehouse/manage/page', { params });
}

// 新增仓库信息
export async function addWarehouseApi(data: AddWarehouse) {
  return requestClient.post<AddWarehouse>('/scm/warehouse/manage/add', data);
}

// 编辑项目信息
export async function editWarehouseApi(data: AddWarehouse) {
  return requestClient.post<AddWarehouse>('/scm/warehouse/manage/edit', data);
}

// 项目详情查看
export async function warehouseDetailApi(id: string) {
  return requestClient.get(`/scm/warehouse/manage/detail/${id}`);
}

// 项目信息提交
export async function warehouseSubmitApi(id: string) {
  return requestClient.post(`/scm/warehouse/manage/submit/${id}`);
}

// 项目信息删除
export async function warehouseDeleteApi(id: string) {
  return requestClient.post(`/scm/warehouse/manage/delete/${id}`);
}

// 项目信息作废
export async function warehouseChangeApi(id: string) {
  return requestClient.post(`/scm/warehouse/manage/change/${id}`);
}

