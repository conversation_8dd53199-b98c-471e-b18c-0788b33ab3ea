import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface InboundPageParams extends BaseDataParams {
  /**
   * 当前页
   */
  current?: string;
  /**
   * 每页的数量
   */
  size?: string;
  /**
   * 正排序规则
   */
  ascs?: string;
  /**
   * 倒排序规则
   */
  descs?: string;
  /**
   * 版本号
   */
  version?: string;
  /**
   * 入库单编号
   */
  inboundReceiptCode?: string;
  /**
   * 入库日期
   */
  receiptDate?: string;
  /**
   * 所属项目ID
   */
  projectId?: string;
  /**
   * 所属项目名称
   */
  projectName?: string;
  /**
   * 所属项目编号
   */
  projectCode?: string;
  /**
   * 仓库ID
   */
  warehouseId?: string;
  /**
   * 仓库编码
   */
  warehouseCode?: string;
  /**
   * 仓库名称
   */
  warehouseName?: string;
  /**
   * 客户企业编码
   */
  customerCompanyCode?: string;
  /**
   * 客户企业名称
   */
  customerCompanyName?: string;
  /**
   * 关联单据类型
   */
  sourceDocumentType?: string;
  /**
   * 关联签收单ID
   */
  deliveryReceiptId?: string;
  /**
   * 关联签收单显示
   */
  deliveryReceiptDisplay?: string;
  /**
   * 货款含税金额
   */
  amountWithTax?: string;
  /**
   * 已开票价税合计
   */
  invoicedAmountWithTax?: string;
  /**
   * 业务状态
   */
  status?: string;
  /**
   * 审批状态
   */
  approvalStatus?: string;
  /**
   * 开票状态
   */
  invoiceStatus?: string;
  /**
   * 备注
   */
  remarks?: string;
  /**
   * 是否按SN码管理 (1:是, 0:否)
   */
  isSnManaged?: string;
  /**
   * 主键
   */
  id?: string;
  /**
   * 创建人
   */
  createBy?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 更新人
   */
  updateBy?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
}

export interface AddInbound extends BaseDataParams {
  /**
   * 主键
   */
  id: number;
  /**
   * 创建人
   */
  createBy: number;
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 更新人
   */
  updateBy: number;
  /**
   * 更新时间
   */
  updateTime: string;
  /**
   * 版本号
   */
  version: number;
  /**
   * 入库单编号
   */
  inboundReceiptCode: string;
  /**
   * 入库日期
   */
  receiptDate: string;
  /**
   * 所属项目ID
   */
  projectId: number;
  /**
   * 所属项目名称
   */
  projectName: string;
  /**
   * 所属项目编号
   */
  projectCode: string;
  /**
   * 仓库ID
   */
  warehouseId: number;
  /**
   * 仓库编码
   */
  warehouseCode: string;
  /**
   * 仓库名称
   */
  warehouseName: string;
  /**
   * 客户企业编码
   */
  customerCompanyCode: string;
  /**
   * 客户企业名称
   */
  customerCompanyName: string;
  /**
   * 关联单据类型
   */
  sourceDocumentType: string;
  /**
   * 关联签收单ID
   */
  deliveryReceiptId: number;
  /**
   * 关联签收单显示
   */
  deliveryReceiptDisplay: string;
  /**
   * 货款含税金额
   */
  amountWithTax: number;
  /**
   * 已开票价税合计
   */
  invoicedAmountWithTax: number;
  /**
   * 业务状态
   */
  status: string;
  /**
   * 审批状态
   */
  approvalStatus: string;
  /**
   * 开票状态
   */
  invoiceStatus: string;
  /**
   * 备注
   */
  remarks: string;
  /**
   * 是否按SN码管理 (1:是, 0:否)
   */
  isSnManaged: number;
  inboundReceiptSourceRelBOS: InboundReceiptSourceRelBOS[];
  inboundReceiptItemBOs: InboundReceiptItemBOs[];
  deleteInboundReceiptItemBOs: InboundReceiptItemBOs[];
}

export interface InboundReceiptItemBOs extends BaseDataParams {
  /**
   * 主键ID
   */
  id: number;
  /**
   * 创建人ID
   */
  createBy: number;
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 更新人ID
   */
  updateBy: number;
  /**
   * 更新时间
   */
  updateTime: string;
  /**
   * 关联的入库单ID
   */
  inboundReceiptId: number;
  /**
   * 本单商品行号
   */
  itemNumber: number;
  /**
   * 商品名称
   */
  productName: string;
  /**
   * 商品编码
   */
  productCode: string;
  /**
   * 商品别名
   */
  productAlias: string;
  /**
   * 计量单位
   */
  measureUnit: string;
  /**
   * 规格文字描述
   */
  specifications: string;
  /**
   * 产地/厂家
   */
  originName: string;
  /**
   * 品牌名称
   */
  brandName: string;
  /**
   * 本次入库数量
   */
  quantity: number;
  /**
   * 入库仓库ID
   */
  warehouseId: number;
  /**
   * SN码/序列号 (多个用逗号分隔)
   */
  serialNumbers: string;
  /**
   * 关联单据类型
   */
  sourceDocumentType: string;
  /**
   * 源单据ID (如采购订单ID, 销售退货单ID)
   */
  sourceDocumentId: number;
  /**
   * 源单据编号 (冗余)
   */
  sourceDocumentDisplay: string;
  /**
   * 源单据商品行ID (如采购订单行ID)
   */
  sourceDocumentItemId: number;
  /**
   * 源单据商品行编号(如采购订单行ID)
   */
  sourceDocumentItemDisplay: number;
  /**
   * 备注
   */
  remarks: string;
  /**
   * 版本号
   */
  version: number;
}

/**
 * 新增的 inboundReceiptSourceRelBOS 类型
 */
export interface InboundReceiptSourceRelBOS extends BaseDataParams {
  /**
   * 主键
   */
  id: number;
  /**
   * 创建人
   */
  createBy: number;
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 更新人
   */
  updateBy: number;
  /**
   * 更新时间
   */
  updateTime: string;
  /**
   * 版本号
   */
  version: number;
  /**
   * 入库单据ID
   */
  inboundReceiptId: number;
  /**
   * 来源单据ID
   */
  sourceDocumentId?: number;
  /**
   * 来源单据展示编号
   */
  sourceDocumentDisplay: string;
  /**
   * 来源单据类型
   */
  sourceDocumentType: string;
}

// 入库列表分页查询
export async function getInboundPageApi(params: PageListParams) {
  return requestClient.get<InboundPageParams[]>('/scm/inbound/receipt/page', { params });
}

// 新增入库信息
export async function addInboundApi(data: AddInbound) {
  return requestClient.post<AddInbound>('/scm/inbound/receipt/add', data);
}

// 入库信息删除
export async function inboundDeleteApi(id: string) {
  return requestClient.post(`/scm/inbound/receipt/delete/${id}`);
}

// 入库信息作废
export async function inboundCancelApi(id: string) {
  return requestClient.post(`/scm/inbound/receipt/cancel/${id}`);
}

// 编辑入库信息
export async function editInboundApi(data: AddInbound) {
  return requestClient.post<AddInbound>('/scm/inbound/receipt/edit', data);
}

// 项目详情查看
export async function inboundDetailApi(id: string) {
  return requestClient.get<AddInbound>(`/scm/inbound/receipt/detail/${id}`);
}

// 入库信息提交
export async function inboundSubmitApi(id: string) {
  return requestClient.post(`/scm/inbound/receipt/submit/${id}`);
}

// 入库列表条件查询
export async function getInboundListApi(id: string) {
  return requestClient.get(`/scm/inbound/receipt/list/${id}`);
}
