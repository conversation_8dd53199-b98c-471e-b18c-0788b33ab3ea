import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface TransOrderPageParams extends BaseDataParams {
  /** 当前页 */
  current?: string;
  /** 每页的数量 */
  size?: string;
  /** 正排序规则 */
  ascs?: string;
  /** 倒排序规则 */
  descs?: string;
  /** 指令单编号 */
  docCode?: string;
  /** 所属项目ID */
  projectId?: string;
  /** 所属项目名称 */
  projectName?: string;
  /** 所属项目编号 */
  projectCode?: string;
  /** 受托企业编码 */
  entrustedCompanyCode?: string;
  /** 受托企业名称 */
  entrustedCompanyName?: string;
  /** 委托日期 */
  entrustedDate?: string;
  /** 业务状态 */
  status?: string;
  /** 审批状态 */
  approvalStatus?: string;
  /** 原始文件ID */
  originalFileId?: string;
  /** 盖章文件ID */
  signedFileId?: string;
  /** 经办人ID */
  userId?: string;
  /** 经办人姓名 */
  userName?: string;
  /** 申请部门ID */
  organId?: string;
  /** 申请部门名称 */
  organName?: string;
  /** 备注说明 */
  description?: string;
  /** 版本号 */
  version?: string;
  /** 主键 */
  id?: string;
}

export interface TransOrderBaseInfo extends BaseDataParams {
  /** 主键 */
  id?: number | string;
  /** 指令单编号 */
  docCode?: string;
  /** 所属项目ID */
  projectId?: number;
  /** 所属项目名称 */
  projectName?: string;
  /** 所属项目编号 */
  projectCode?: string;
  /** 受托企业编码 */
  entrustedCompanyCode?: string;
  /** 受托企业名称 */
  entrustedCompanyName?: string;
  /** 委托日期 */
  entrustedDate?: string;
  /** 业务状态 */
  status?: string;
  /** 审批状态 */
  approvalStatus?: string;
  /** 原始文件ID */
  originalFileId?: number;
  /** 盖章文件ID */
  signedFileId?: number;
  /** 经办人ID */
  userId?: number;
  /** 经办人姓名 */
  userName?: string;
  /** 申请部门ID */
  organId?: number;
  /** 申请部门名称 */
  organName?: string;
  /** 备注说明 */
  description?: string;
  /** 版本号 */
  version?: number;
  /** 备注 */
  remarks?: string;
  /** 业务附件 */
  attachmentList?: number[];
}

// 分页查询
export async function getTransportPageApi(params: PageListParams) {
  return requestClient.get<TransOrderPageParams>('/scm/transport/manage/page', { params });
}

// 新增
export async function addTransportApi(data: TransOrderBaseInfo) {
  return requestClient.post<TransOrderBaseInfo>('/scm/transport/manage/add', data);
}

// 编辑
export async function editTransportApi(data: TransOrderBaseInfo) {
  return requestClient.post<TransOrderBaseInfo>('/scm/transport/manage/edit', data);
}

// 提交
export async function submitTransportApi(data: TransOrderBaseInfo) {
  return requestClient.post<TransOrderBaseInfo>('/scm/transport/manage/submit', data);
}

// 详情
export async function detailTransportApi(id: string) {
  return requestClient.get(`/scm/transport/manage/detail/${id}`);
}

// 删除
export async function delTransportApi(id: string) {
  return requestClient.post(`/scm/transport/manage/delete/${id}`);
}
