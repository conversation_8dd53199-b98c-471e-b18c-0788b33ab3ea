import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface ShipmentPageParams extends BaseDataParams {
  /** 当前页 */
  current?: string;
  /** 每页的数量 */
  size?: string;
  /** 正排序规则 */
  ascs?: string;
  /** 倒排序规则 */
  descs?: string;
  /** 主键 */
  id?: string;
  /** 创建时间 */
  createTime?: string;
  /** 创建人ID */
  createBy?: string;
  /** 修改时间 */
  updateTime?: string;
  /** 修改人ID */
  updateBy?: string;
  /** 标记删除 */
  deleteFlag?: string;
  /** 版本号 */
  version?: string;
  /** 发运单编号 (系统生成) */
  shipmentCode?: string;
  /** 所属项目ID */
  projectId?: string;
  /** 所属项目名称 */
  projectName?: string;
  /** 所属项目编码 */
  projectCode?: string;
  /** 发货类型 */
  shipmentType?: string;
  /** 运输方式 */
  transportMethod?: string;
  /** 运输企业编码 */
  carrierCompanyCode?: string;
  /** 运输企业名称 */
  carrierCompanyName?: string;
  /** 收货公司编码 */
  consigneeCompanyCode?: string;
  /** 收货公司名称 */
  consigneeCompanyName?: string;
  /** 结算公司编码 */
  billingCompanyCode?: string;
  /** 结算公司名称 */
  billingCompanyName?: string;
  /** 发运日期 */
  shipmentDate?: string;
  /** 收货地址 */
  receiptAddress?: string;
  /** 状态 */
  status?: string;
  /** 运输总费用 */
  totalShipmentCost?: string;
  /** 备注 */
  remarks?: string;
  /** 源单据编号 */
  sourceDocumentDisplays?: string;
}

export interface ShipmentBaseInfo extends BaseDataParams {
  id?: number; // 主键
  createTime?: string; // 创建时间
  createBy?: number; // 创建人ID
  updateTime?: string; // 修改时间
  updateBy?: number; // 修改人ID
  deleteFlag?: boolean; // 标记删除
  version?: number; // 版本号
  shipmentCode?: string; // 发运单编号 (系统生成)
  projectId?: number; // 所属项目ID
  projectName?: string; // 所属项目名称
  projectCode?: string; // 所属项目编码
  shipmentType?: string; // 发货类型
  transportMethod?: string; // 运输方式
  carrierCompanyCode?: string; // 运输企业编码
  carrierCompanyName?: string; // 运输企业名称
  consigneeCompanyCode?: number; // 收货公司编码
  consigneeCompanyName?: string; // 收货公司名称
  billingCompanyCode?: string; // 结算公司编码
  billingCompanyName?: string; // 结算公司名称
  shipmentDate?: string; // 发运日期
  receiptAddress?: string; // 收货地址
  status?: string; // 状态
  totalShipmentCost?: number; // 运输总费用
  remarks?: string; // 备注
  shipmentDeliveryList: ShipmentDeliveryBO[]; // 发货明细
  shipmentItemList: ShipmentItemBO[]; // 商品信息
  shipmentSourceRelList: ShipmentSourceRelBO[]; // 关联源单据信息
  attachmentList: number[]; // 业务附件
  province?: string; // 省份
  city?: string; // 城市
  district?: string; // 区县
  detailAddress?: string; // 详细地址
  plannedDeliveryDate?: string; // 计划收货时间
  receiptDistrict?: string; // 收货区县
  receiptDetailAddress?: string; // 收货详细地址
  receiptProvince?: string; // 收货省份
  receiptCity?: string; // 收货城市
  documentType?: string; // 单据类型
  executorCompanyName?: string; // 执行方公司名称
}

// 发货明细
export interface ShipmentDeliveryBO {
  id?: number; // 主键
  version?: number; // 版本号
  shipmentId?: number; // 关联的发货单ID
  deliveryType?: string; // 发货类型
  deliveryNumber?: string; // 车辆号/船舶号/物流号
  contactName?: string; // 联系人姓名
  contactPhone?: string; // 联系人电话
  remarks?: string; // 备注
}

// 商品信息
export interface ShipmentItemBO {
  id?: number; // 主键
  version?: number; // 版本号
  shipmentId?: number; // 关联的发运单ID
  productCode?: string; // 商品编码
  productName?: string; // 商品名称
  productAlias?: string; // 商品别名
  specifications?: string; // 规格型号
  measureUnit?: string; // 计量单位
  brandName?: string; // 牌号/品牌
  originName?: string; // 产地/厂家
  shippedQuantity?: number; // 本次发运数量/重量
  sourceDocumentItemNumber?: number; // 源单据商品行ID(如采购订单行ID)
  sourceDocumentId?: number; // 源单据ID (如采购订单ID)
  sourceDocumentCode?: string; // 源单据编号
  sourceDocumentName?: string; // 源单据名称
  remarks?: string; // 备注
}

// 源单据信息
export interface ShipmentSourceRelBO {
  id?: number; // 主键
  version?: number; // 版本号
  orderName?: string; // 源单据名称
  orderCode?: string; // 源单据编号
  shipmentId?: number; // 关联的发运单ID
  sourceDocumentId?: number; // 源单据ID (如采购订单ID)
  sourceDocumentCode?: string; // 源单据编号
  sourceDocumentName?: string; // 源单据名称
  sourceDocumentType?: string; // 源单据类型 (如: PURCHASE_ORDER, SALES_RETURN)
}

export interface SignShipment extends BaseDataParams {
  id?: number; // 主键
  createTime?: string; // 创建时间
  createBy?: number; // 创建人ID
  updateTime?: string; // 修改时间
  updateBy?: number; // 修改人ID
  deleteFlag?: boolean; // 标记删除
  version?: number; // 版本号
  shipmentId: string; // 发运单编号
  signDate: string; // 签收日期
  signerName: string; // 签收人姓名
  receiptFileId: string; // 签收文件Id
  remarks?: string; // 备注
}

// 发货列表分页查询
export async function getShipmentPageApi(params: PageListParams) {
  return requestClient.get<ShipmentPageParams>('/scm/shipment/manage/page', { params });
}

// 新增发运单
export async function addShipmentApi(data: ShipmentBaseInfo) {
  return requestClient.post<ShipmentBaseInfo>('/scm/shipment/manage/add', data);
}

// 编辑发运单
export async function editShipmentApi(data: ShipmentBaseInfo) {
  return requestClient.post<ShipmentBaseInfo>('/scm/shipment/manage/edit', data);
}

// 发货详情查询
export async function detailShipmentApi(id: string) {
  return requestClient.get(`/scm/shipment/manage/detail/${id}`);
}

// 发货信息删除
export async function deleteShipmentApi(id: string) {
  return requestClient.post(`/scm/shipment/manage/delete/${id}`);
}

// 发货信息作废
export async function cancelShipmentApi(id: string) {
  return requestClient.post(`/scm/shipment/manage/cancel/${id}`);
}

// 确认签收
export async function signShipmentApi(data: SignShipment) {
  return requestClient.post<SignShipment>('/scm/shipment/manage/sign', data);
}
