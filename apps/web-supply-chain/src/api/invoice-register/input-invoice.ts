import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface InputInvoicePageParams extends BaseDataParams {

}

export interface InputInvoice extends BaseDataParams {

}


// 进项税票列表分页查询
export async function inputInvoicePageApi(params: PageListParams) {
  return requestClient.get<InputInvoicePageParams[]>('/scm/input/invoice/page', { params });
}

// 新增进项税票
export async function inputInvoiceAddApi(data: InputInvoice) {
  return requestClient.post<InputInvoice>('/scm/input/invoice/add', data);
}

// 进项税票删除
export async function inputInvoiceDeleteApi(id: string) {
  return requestClient.post(`/scm/input/invoice/delete/${id}`);
}

// 编辑进项税票
export async function inputInvoiceEditApi(data: InputInvoice) {
  return requestClient.post<InputInvoice>('/scm/input/invoice/edit', data);
}

// 进项税票详情查询
export async function inputInvoiceDetailApi(id: string) {
  return requestClient.get(`/scm/input/invoice/detail/${id}`);
}

// 进项税票提交
export async function inputInvoiceSubmitApi(id: string) {
  return requestClient.post(`/scm/input/invoice/submit/${id}`);
}

// 进项税票列表查询
export async function inputInvoiceListApi(params: PageListParams) {
  return requestClient.get<InputInvoicePageParams[]>('/scm/input/invoice/list', { params });
}
