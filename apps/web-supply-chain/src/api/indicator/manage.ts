import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

// IndicatorListInfo，数据
export interface IndicatorListInfo {
  // 调整规则列表
  adjustRules?: ScoringAdjustRuleBO[];
  // 评分分类及指标列表
  categoryIndicators?: CategoryIndicatorBO[];
  // 评分描述
  description?: string;
  // 评分模型ID
  id?: number;
  // 评分模型状态，如果为0，表示不是最新版本，如果为1，表示最新版本
  state?: number;
  [property: string]: any;
}

// ScoringAdjustRuleBO，调整规则列表
export interface ScoringAdjustRuleBO {
  // 主键
  id?: number;
  // 规则因子
  ruleFactor?: number;
  // 规则名称
  ruleName?: string;
  // 显示顺序
  sortCode?: number;
  [property: string]: any;
}

// CategoryIndicatorBO，评分分类及指标列表
export interface CategoryIndicatorBO {
  // 分类主键
  categoryId?: number;
  // 分类名称 (例如: 基础分, 加分项, 减分项)
  categoryName?: string;
  // 显示顺序
  categorySortCode?: number;
  // 指标主键
  indicatorId?: number;
  // 指标名称
  indicatorName?: string;
  // 该指标下的规则列表
  indicatorRules?: ScoringIndicatorRuleBO[];
  // 排序
  indicatorSortCode?: number;
  // NUMBER, SELECT
  inputType?: string;
  // 分数限制 (0:不限制, 1:必须为零或正分, -1:必须为零或负分)
  scoreLimit?: number;
  // 权重
  weight?: number;
  [property: string]: any;
}

// ScoringIndicatorRuleBO，该指标下的规则列表
export interface ScoringIndicatorRuleBO {
  // 主键
  id?: number;
  // 规则描述 (例如: 5年以上, (1000,3000万], 规上企业)
  ruleName?: string;
  // 对应的分数 (可以是正分或负分)
  ruleScore?: number;
  // 显示顺序
  sortCode?: number;
  [property: string]: any;
}

// ScoringCompanySaveRequest
export interface CompanyIndicatorInfo {
  // 审批状态 (PENDING:待审批, APPROVED:已通过, REJECTED:已驳回)
  approvalStatus?: string;
  // 企业编码
  companyCode?: string;
  // 企业名称
  companyName?: string;
  // 最终总分 (经过调整因子计算后)
  finalScore?: number;
  // 主键
  id?: number;
  // 测算总分 (初始得分，未经过调整因子)
  initialScore?: number;
  // 关联项目ID
  projectId?: number;
  // 评分配置Id
  scoringConfigId?: number;
  // 测算日期
  scoringDate?: Date;
  scoringDetails?: { [key: string]: any };
  // 测算额度上限
  scoringLimit?: number;
  // 状态
  status?: string;
  [property: string]: any;
}
// ScoringCompanyVO
export interface ScoringCompanyVO {
  // 审批状态 (PENDING:待审批, APPROVED:已通过, REJECTED:已驳回)
  approvalStatus?: string;
  // 企业编码
  companyCode?: string;
  // 企业名称
  companyName?: string;
  // 最终总分 (经过调整因子计算后)
  finalScore?: number;
  // 主键
  id?: number;
  // 测算总分 (初始得分，未经过调整因子)
  initialScore?: number;
  // 关联项目ID
  projectId?: number;
  // 评分配置Id
  scoringConfigId?: number;
  // 测算日期
  scoringDate?: Date;
  // 评分详情（动态结构）
  scoringDetails?: { [key: string]: any };
  // 测算额度上限
  scoringLimit?: number;
  // 状态
  status?: string;
  [property: string]: any;
}

export async function getCompanyIndicatorPageListApi(params: PageListParams) {
  return requestClient.post('/scm/scoring/company/page', {}, { params });
}
export async function getIndicatorConfigListApi(params?: { id?: number }) {
  return requestClient.get<IndicatorListInfo>('/scm/scoring/config/indicator/list', { params });
}
export async function saveCompanyIndicatorApi(data: CompanyIndicatorInfo) {
  return requestClient.post('/scm/scoring/company/save', data);
}
export async function submitCompanyIndicatorApi(data: CompanyIndicatorInfo) {
  return requestClient.post('/scm/scoring/company/submit', data);
}
export async function getCompanyIndicatorHistoryApi(params: { companyCode: string }) {
  return requestClient.get<CompanyIndicatorInfo[]>('/scm/scoring/company/history/list', { params });
}
export async function getCompanyIndicatorDetailApi(params: { id: number }) {
  return requestClient.get<ScoringCompanyVO>('/scm/scoring/company/detail', { params });
}
