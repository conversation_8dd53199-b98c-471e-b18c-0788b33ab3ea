import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

/**
 * 预警信息数据接口
 */
export interface EarlyWarningItemInfo {
  // 附件列表
  attachmentList?: number[];
  // 预警查询开始日期
  beginDate?: Date;
  // 业务单据号 (冗余)
  businessDocumentCode?: string;
  // 业务单据ID (如: wms_inbound_receipt.id)
  businessDocumentId?: number;
  // 业务名 (冗余)
  businessDocumentName?: string;
  // 业务单据类型 (如: WMS_INBOUND_RECEIPT)
  businessDocumentType?: string;
  // 预警相关企业code
  companyCode?: string;
  // 预警相关企业名称
  companyName?: string;
  // 创建人名称
  createByName?: string;
  // 处置日期
  disposalDate?: Date;
  // 风险缓释措施描述
  disposalDescription?: string;
  // 监测处置措施
  disposalMethod?: string;
  // 处置状态
  disposalStatus?: string;
  // 处置人
  disposalUser?: string;
  // 预警查询结束日期
  endDate?: Date;
  // 主键
  id?: number;
  // 最后触发日期
  lastTriggerDate?: Date;
  // 最后触发原因
  lastTriggerReason?: string;
  // 项目编码
  projectCode?: string;
  // 项目ID
  projectId?: number;
  // 项目名称
  projectName?: string;
  // 预警记录ID
  recordCode?: string;
  // 预警规则描述
  ruleDesc?: string;
  // 预警规则ID
  ruleId?: number;
  // 监管类型规则名称
  ruleName?: string;
  // 预警状态
  status?: string;
  // 更新人名称
  updateByName?: string;
  // 版本号
  version?: number;
  // 预警记录列表
  warningRecordLogList?: WarningRecordLogBO[];
  warningRuleBO?: WarningRuleBO;
  [property: string]: any;
}

/**
 * WarningRecordLogBO，预警记录列表
 */
export interface WarningRecordLogBO {
  // 主键
  id?: number;
  // 预警明细ID
  recordId?: number;
  // 预警日期
  triggerDate?: Date;
  // 预警描述
  triggerReason?: string;
  // 版本号
  version?: number;
  [property: string]: any;
}

/**
 * WarningRuleBO，预警规则信息
 */
export interface WarningRuleBO {
  // 触发条件描述
  conditionDesc?: string;
  // 主键
  id?: number;
  // 是否短信提醒
  isSms?: number;
  // 通知角色
  notifyRole?: string;
  // 预警规则描述
  ruleDesc?: string;
  // 预警规则名称
  ruleName?: string;
  smsUserIdList?: number[];
  // 短信接收人用户ID
  smsUserIds?: string;
  // 短信接收人用户名称
  smsUserNames?: string;
  // 状态
  status?: string;
  // 触发条件值
  value?: number;
  // 触发条件类型
  valueType?: string;
  // 版本号
  version?: number;
  // 预警等级
  warningLevel?: string;
  [property: string]: any;
}

export async function getEarlyWarningItemPageListApi(params: PageListParams) {
  return requestClient.get<EarlyWarningItemInfo[]>('/scm/risk/warn/record/page', { params });
}
export async function getEarlyWarningItemDetailApi(params: { id: number }) {
  return requestClient.get<EarlyWarningItemInfo>('/scm/risk/warn/record/detail', { params });
}
export async function handleEarlyWarningItemApi(data: EarlyWarningItemInfo) {
  return requestClient.post('/scm/risk/warn/record/deal', data);
}
