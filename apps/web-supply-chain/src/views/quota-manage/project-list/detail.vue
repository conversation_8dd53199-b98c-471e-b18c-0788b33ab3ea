<script setup lang="ts">
import type { EchartsUIType } from '@vben/plugins/echarts';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { CompanylimitInfo } from '#/api/quota-manage';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP, DETAIL_GRID_OPTIONS } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { useDictStore } from '@vben/stores';

import { detailProjectApi, getProjectLogApi } from '#/api/quota-manage';

const dictStore = useDictStore();
const infoDetail = ref<CompanylimitInfo>({});
const chartRefPie = ref<EchartsUIType>();
const chartRefLine = ref<EchartsUIType>();
const init = async (data: CompanylimitInfo) => {
  infoDetail.value = await detailProjectApi({ id: data.id || '' });

  const usedMonthLimitBOList = infoDetail.value.usedMonthLimitBOList?.map((item) => item.limitAmount) || [];
  const releaseMonthLimitBOList = infoDetail.value.releaseMonthLimitBOList?.map((item) => item.limitAmount) || [];
  const monthList = infoDetail.value.usedMonthLimitBOList?.map((item) => item.month) || [];
  const max = Math.max(...usedMonthLimitBOList, ...releaseMonthLimitBOList);
  const { renderEcharts: renderEchartsPie } = useEcharts(chartRefPie);
  const { renderEcharts: renderEchartsLine } = useEcharts(chartRefLine);
  renderEchartsPie({
    title: [
      {
        text: '额度使用情况',
        left: 'left',
        textStyle: {
          fontSize: 15,
        },
      },
      {
        text: `授信额度\n${infoDetail.value.totalLimit || 0}元`,
        left: 'center',
        top: 'center',
        textStyle: {
          fontSize: 14,
          color: '#333',
          lineHeight: 24,
          align: 'center',
        },
      },
    ],
    tooltip: {
      trigger: 'item',
    },
    series: [
      {
        name: '额度使用情况',
        type: 'pie',
        radius: ['50%', '70%'],
        data: [
          { value: Number(infoDetail.value.frozenLimit) || 0, name: '冻结额度' },
          { value: Number(infoDetail.value.usedLimit) || 0, name: '已用额度' },
          { value: Number(infoDetail.value.availableLimit) || 0, name: '可用额度' },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
          label: {
            show: true,
            fontSize: '14',
            formatter: '{b}\n{d}%',
          },
        },
      },
    ],
  });
  renderEchartsLine({
    title: {
      text: `月度用款/还款曲线`,
      left: 'center',
      textStyle: {
        fontSize: 16,
      },
    },
    grid: {
      bottom: 10,
      containLabel: true,
      left: '1%',
      right: '1%',
      top: '15%',
    },
    series: [
      {
        data: usedMonthLimitBOList,
        type: 'line',
        smooth: true,
        itemStyle: {
          color: 'green',
        },
      },
      {
        data: releaseMonthLimitBOList,
        type: 'line',
        smooth: true,
        itemStyle: {
          color: 'blue',
        },
      },
    ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        lineStyle: {
          color: '#019680',
          width: 1,
        },
      },
      formatter: (params: any) => {
        return `<div>
                  <div>${params[0].name}</div>
                  <div>还款: ${params[0].value}</div>
                  <div>用款: ${params[1].value}</div>
                </div>`;
      },
    },
    xAxis: {
      type: 'category',
      data: monthList,
      axisTick: {
        show: false,
      },
      boundaryGap: false,
      splitLine: {
        lineStyle: {
          type: 'solid',
          width: 1,
        },
        show: true,
      },
    },
    yAxis: [
      {
        type: 'value',
        name: '单位：万元',
        nameLocation: 'end',
        nameTextStyle: {
          padding: [0, 0, 0, 10],
        },
        axisLabel: {
          formatter: '{value}',
        },
        axisTick: {
          show: false,
        },
        max: max * 1.2,
        splitArea: {
          show: true,
        },
        splitNumber: 4,
      },
    ],
  });
};
const GridOptions: VxeTableGridOptions = {
  columns: [
    {
      field: 'transactionType',
      title: '用信类型',
      minWidth: '160px',
      formatter: ['formatStatus', 'CREDIT_CHANGE_TYPE'],
    },
    { field: 'transactionType1', title: '操作类型', minWidth: '160px' },
    { field: 'changeTime', title: '变动时间', minWidth: '160px' },
    { field: 'changeAmount', title: '变动额度（元）', minWidth: '160px' },
    { field: 'sourceDocumentType', title: '单据类型', minWidth: '160px' },
    { field: 'sourceDocumentCode', title: '单据编号', minWidth: '160px' },
    { field: 'projectCode', title: '项目编号', minWidth: '160px' },
    { field: 'projectName', title: '项目名称', minWidth: '160px' },
    { field: 'updateBy1', title: '创建部门', minWidth: '160px' },
    { field: 'createBy', title: '创建人', minWidth: '160px' },
    { field: 'createTime', title: '创建时间', minWidth: '160px' },
  ],
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getProjectLogApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  ...DETAIL_GRID_OPTIONS,
  pagerConfig: {
    enabled: true,
  },
};

const [Grid, GridApi] = useVbenVxeGrid({
  gridOptions: GridOptions,
});
const [registerPopup] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" title="额度信息" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
        <a-descriptions-item label="额度主体">
          {{ infoDetail.projectName }}
        </a-descriptions-item>
        <a-descriptions-item label="项目编号">
          {{ infoDetail.projectCode }}
        </a-descriptions-item>
        <a-descriptions-item label="授信额度(元)">{{ infoDetail.totalLimit }} </a-descriptions-item>
        <a-descriptions-item label="担保企业">
          {{ infoDetail.guaranteeCompanyName }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="融资额度分析" />
      <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
        <a-descriptions-item label="额度记录编号">
          {{ infoDetail.limitCode }}
        </a-descriptions-item>
        <a-descriptions-item label="授信总额度(元)">
          {{ infoDetail.totalLimit }}
        </a-descriptions-item>
        <a-descriptions-item label="已用额度(元)">
          {{ infoDetail.usedLimit }}
        </a-descriptions-item>
        <a-descriptions-item label="可用额度(元)">
          {{ infoDetail.availableLimit }}
        </a-descriptions-item>
        <a-descriptions-item label="累计用款金额(元)">
          {{ infoDetail.totalOutAmount }}
        </a-descriptions-item>
        <a-descriptions-item label="累计回款金额(元)"> {{ infoDetail.totalBackAmount }} </a-descriptions-item>
      </a-descriptions>
      <div class="chart-con">
        <div class="chart-bar">
          <EchartsUI ref="chartRefPie" />
        </div>
        <div class="chart-bar"><EchartsUI ref="chartRefLine" /></div>
      </div>
      <BasicCaption content="额度变动明细" />
      <Grid />
    </div>
  </BasicPopup>
</template>

<style lang="less" scoped>
.chart-con {
  display: flex;
  justify-content: space-between;
  .chart-bar {
    border: 1px solid #ddd;
    padding: 10px;
    &:first-child {
      width: 30%;
    }
    &:last-child {
      width: 65%;
    }
  }
}
</style>
