<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { AddInbound } from '#/api';

import { Page } from '@vben/common-ui';
// import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Modal as AntdModal, Button, Space, TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
// import { getInboundPageApi, inboundDeleteApi } from '#/api';
import {usePopup} from "@vben/fe-ui";

const dictStore = useDictStore();

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'inboundReceiptCode',
      label: '企业名称',
    },
    {
      component: 'Select',
      fieldName: 'inboundReceiptCode',
      label: '风险类别',
      // componentProps: {
      //   options: dictStore.getDictList('business_structure'), // 补全字典类型
      // },
    },
    {
      component: 'Select',
      fieldName: 'inboundReceiptCode',
      label: '风险级别',
      // componentProps: {
      //   options: dictStore.getDictList('business_structure'), // 补全字典类型
      // },
    },
    {
      component: 'Select',
      fieldName: 'inboundReceiptCode',
      label: '风险类型',
      // componentProps: {
      //   options: dictStore.getDictList('business_structure'), // 补全字典类型
      // },
    },
    {
      component: 'RangePicker',
      fieldName: 'receiptDate',
      label: '变动日期',
    },
  ],
  fieldMappingTime: [
    // 将 receiptDate 数组映射到 receiptStartDate 和 receiptEndDate 字段
    ['receiptDate', ['receiptStartDate', 'receiptEndDate'], 'YYYY-MM-DD'],
  ],
  showCollapseButton: true,
  submitOnEnter: true,
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'warehouseName', title: '查询日期' },
    { field: 'warehouseCompanyName', title: '企业名称' },
    { field: 'province', title: '风险级别' },
    { field: 'detailAddress', title: '风险分类' },
    { field: 'isLocationManaged', title: '风险类型' },
    { field: 'createTime', title: '事项' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 120,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getInboundPageApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [registerForm, { openPopup: openFormPopup }] = usePopup();

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 查看
const detail = (row: AddInbound) => {
  openFormPopup(true, row);
  console.log('查看', row);
};

</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #action="{ row }">
        <Space>
          <TypographyLink type="danger" @click="detail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
        </Space>
      </template>
    </Grid>
  </Page>
</template>

<style></style>
