<script setup lang="ts">
import type { OperationSuperviseBaseInfo } from '#/api';

import { reactive, ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import { cloneDeep, formatDate } from '@vben/utils';

import { Descriptions, DescriptionsItem } from 'ant-design-vue';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { getInspectionPageApi, operationSuperviseDetailApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';
import { getMultiDictLabels } from '#/utils/data-process';

const emit = defineEmits(['register', 'ok']);

// const { getDictList } = useDictStore();
const { useViewButton, initWorkflow, isWorkflow, isRunningTask } = useWorkflowBase();
const pageType = ref('detail');
const ViewButton = useViewButton();
const pageLoading = ref(false);

// 默认数据
const defaultForm: Partial<OperationSuperviseBaseInfo> = {
  id: undefined,
  reportCode: undefined,
  reportName: undefined,
  projectId: undefined,
  projectName: undefined,
  status: undefined,
  approvalStatus: undefined,
  reportDate: undefined,
  inspectionId: undefined,
  remarks: undefined,
  attachmentList: [],
};

const detailForm = reactive<Partial<OperationSuperviseBaseInfo>>(cloneDeep(defaultForm));

const inspectionOptions = ref([]);

// 获取检查项列表
const fetchInspectionOptions = async (params?: any) => {
  try {
    const res = await getInspectionPageApi(params);
    // 假设返回的数据结构中，列表数据在 res.data.records 中
    inspectionOptions.value = res.records.map((item: any) => ({
      label: item.reportName,
      value: item.id,
      ...item,
    }));
  } catch (error) {
    console.error('获取检查项列表失败:', error);
  }
};

const title = '详细内容';

const init = async (data: any) => {
  pageLoading.value = true;
  pageType.value = data.pageType;
  await initWorkflow({ formKey: 'scm_operation_supervision', businessKey: data.id });
  await fetchInspectionOptions();
  if (data.id) {
    const res = await operationSuperviseDetailApi(data.id);
    // 深度复制确保响应性
    Object.keys(res).forEach((key) => {
      detailForm[key] = res[key];
    });
  } else {
    Object.assign(detailForm, cloneDeep(defaultForm));
  }
  pageLoading.value = false;
};

const [registerPopup] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" :loading="pageLoading" :title="title" @register="registerPopup">
    <template #insertToolbar>
      <ViewButton v-if="isWorkflow && pageType === 'detail'" />
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <Descriptions v-bind="DESCRIPTIONS_PROP">
        <DescriptionsItem label="报告名称">
          <span v-if="detailForm.reportName">
            {{ detailForm.reportName }}
          </span>
        </DescriptionsItem>

        <DescriptionsItem label="关联运营检查">
          <span v-if="detailForm.inspectionId">
            {{ getMultiDictLabels(inspectionOptions, detailForm.inspectionId) }}
          </span>
        </DescriptionsItem>

        <DescriptionsItem label="报告日期">
          <span v-if="detailForm.reportDate">
            {{ formatDate(detailForm.reportDate, 'YYYY-MM-DD') }}
          </span>
        </DescriptionsItem>

        <DescriptionsItem label="分析报告编号">
          <span v-if="detailForm.reportCode">
            {{ detailForm.reportCode }}
          </span>
        </DescriptionsItem>

        <DescriptionsItem label="备注">
          <span v-if="detailForm.remarks">
            {{ detailForm.remarks }}
          </span>
        </DescriptionsItem>
      </Descriptions>

      <!-- 附件信息 -->
      <BaseAttachmentList
        v-model="detailForm.attachmentList"
        :business-id="detailForm.id"
        business-type="SCM_OPERATION_SUPERVISION"
      />
    </div>
  </BasicPopup>
</template>

<style scoped>
:where(.css-dev-only-do-not-override-1gaak89).ant-picker {
  width: 100%;
}
</style>
