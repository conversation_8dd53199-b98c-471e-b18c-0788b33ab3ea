<script setup lang="ts">
import { computed, reactive, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { cloneDeep } from '@vben/utils';
import { BaseAttachmentList } from '#/adapter/base-ui';

import { Col, DatePicker, Form, FormItem, Input, message, Row, Select, Textarea } from 'ant-design-vue';
import { $t } from '@vben/locales';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

import {
  type OperationSuperviseBaseInfo,
  type InspectionInfo,
  operationSuperviseAddApi,
  operationSuperviseEditApi,
  operationSuperviseDetailApi,
  operationSuperviseSubmitApi,
  getInspectionListApi,
} from '#/api';
import type { Rule } from 'ant-design-vue/es/form';

const emit = defineEmits(['register', 'ok']);

const { startWorkflow, WorkflowPreviewModal, useOperationButton, initWorkflow, isProcessInstance, isWorkflowLoading } =
  useWorkflowBase();
const pageType = ref('edit');
const OperationButton = useOperationButton();
const colSpan = { md: 12, sm: 24 };
const pageLoading = ref(false);
const loading = reactive({
  submit: false,
});

// 默认数据
const defaultForm: Partial<OperationSuperviseBaseInfo> = {
  id: undefined,
  reportCode: undefined,
  reportName: undefined,
  projectId: undefined,
  projectName: undefined,
  status: undefined,
  approvalStatus: undefined,
  reportDate: undefined,
  inspectionId: undefined,
  remarks: undefined,
  attachmentList: [],
};

let detailForm = reactive<Partial<OperationSuperviseBaseInfo>>(cloneDeep(defaultForm));

const inspectionOptions = ref<InspectionInfo[]>([]);

// 获取检查项列表
const fetchInspectionOptions = async (params?: any) => {
  try {
    const res = await getInspectionListApi(params);
    inspectionOptions.value = [];
    // 将每个元素的 id 转换为字符串并赋值给 value 属性
    const formattedOptions = res.map((item) => ({
      ...item,
      value: String(item.id), // 将 id 转换为字符串并赋值给 value
    }));
    inspectionOptions.value = cloneDeep(formattedOptions);
  } catch (error) {
    console.error('获取检查项列表失败:', error);
  }
};

const rules: Record<string, Rule[]> = {
  reportName: [{ required: true, message: '报告名称', trigger: 'change' }],
  inspectionId: [{ required: true, message: '关联运营检查', trigger: 'change' }],
};

const title = computed(() => {
  return detailForm.id ? '编辑项目' : '新增项目';
});

const init = async (data: any) => {
  pageLoading.value = true;
  pageType.value = data.pageType;
  await initWorkflow({ formKey: 'scm_operation_supervision', businessKey: data.id });
  await fetchInspectionOptions();
  if (data.id) {
    const res = await operationSuperviseDetailApi(data.id);
    // 深度复制确保响应性
    Object.keys(res).forEach((key) => {
      // 特殊处理 inspectionId，确保是数组格式
      if (key === 'inspectionId') {
        detailForm[key] = Array.isArray(res[key]) ? res[key] : typeof res[key] === 'string' ? res[key].split(',') : [];
      } else {
        detailForm[key] = res[key];
      }
    });
  } else {
    Object.assign(detailForm, cloneDeep(defaultForm));
  }
  pageLoading.value = false;
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const close = () => {
  Object.assign(detailForm, cloneDeep(defaultForm));
};
const formRef = ref();
const save = async (type: string) => {
  pageLoading.value = true;
  try {
    await formRef.value.validate();
    changeOkLoading(true);
    const submitData = detailForm as Required<OperationSuperviseBaseInfo>;

    // 处理inspectionId，确保是字符串格式
    if (Array.isArray(submitData.inspectionId)) {
      submitData.inspectionId = submitData.inspectionId.join(',');
    }
    let api = detailForm.id ? operationSuperviseEditApi : operationSuperviseAddApi;
    if (type === 'submit') {
      const { processDefinitionKey, startUserSelectAssignees } = await startWorkflow();
      detailForm.processDefinitionKey = processDefinitionKey;
      detailForm.startUserSelectAssignees = startUserSelectAssignees;
      api = operationSuperviseSubmitApi;
    }

    const res = await api(submitData);

    // 使用国际化提示保存成功
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } catch (error) {
    message.error('保存失败，请检查网络或输入内容');
    console.error('新增仓库失败:', error);
    changeOkLoading(false); // 防止 loading 卡住
  } finally {
    changeOkLoading(false);
  }
  pageLoading.value = false;
  close();
};

const labelCol = { style: { width: '150px' } };
const workflowSuccess = () => {
  emit('ok');
  closePopup();
};
</script>

<template>
  <BasicPopup v-bind="$attrs" :loading="pageLoading" :title="title" @register="registerPopup" @close="close">
    <template #insertToolbar>
      <a-space>
        <a-button v-if="pageType !== 'audit'" type="primary" :loading="loading.submit" @click="save('save')">
          保存
        </a-button>
        <a-button v-if="pageType !== 'audit'" type="primary" :loading="loading.submit" @click="save('submit')">
          提交
        </a-button>
      </a-space>
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton v-if="isProcessInstance" @success="workflowSuccess" />
      </div>
    </template>
    <Form
      ref="formRef"
      :colon="false"
      :model="detailForm"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <!-- 基本信息 -->
      <BasicCaption content="基本信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="报告名称" name="reportName">
            <Input v-model:value="detailForm.reportName" :disabled="pageType === 'audit'" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="关联运营检查" name="inspectionId">
            <Select
              v-model:value="detailForm.inspectionId"
              show-search
              mode="multiple"
              :options="inspectionOptions"
              :field-names="{ label: 'reportName', value: 'value' }"
              :filter-option="
                (input: string, option: any) => {
                  return option.reportName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                }
              "
              style="width: 100%"
              :disabled="pageType === 'audit'"
            />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="报告日期" name="reportDate">
            <DatePicker
              v-model:value="detailForm.reportDate"
              value-format="YYYY-MM-DD hh:mm:ss"
              format="YYYY-MM-DD"
              :disabled="pageType === 'audit'"
            />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="分析报告编号" name="reportCode">
            <span v-if="detailForm.id">
              {{ detailForm.reportCode }}
            </span>
            <span v-else> - </span>
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="备注" name="remarks">
            <Textarea v-model:value="detailForm.remarks" :rows="3" :disabled="pageType === 'audit'" />
          </FormItem>
        </Col>
      </Row>

      <!-- 附件信息 -->
      <BaseAttachmentList
        v-model="detailForm.attachmentList"
        :business-id="detailForm.id"
        business-type="SCM_OPERATION_SUPERVISION"
        :edit-mode="pageType !== 'audit'"
      />
    </Form>
    <WorkflowPreviewModal v-if="pageType === 'edit'" />
  </BasicPopup>
</template>

<style scoped>
.ant-input-number,
.ant-picker {
  width: 100% !important;
}

.ant-picker-focused {
  width: 100% !important;
}
</style>
