<script setup lang="ts">
import type { InspectionInfo } from '#/api';

import { reactive, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { BaseFilePreview } from '#/adapter/base-ui';
import { getInspectionDetailApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

const emit = defineEmits(['register', 'ok']);
const {
  useOperationButton,
  useViewButton,
  initWorkflow,
  isProcessInstance,
  isWorkflow,
  isRunningTask,
  isWorkflowLoading,
} = useWorkflowBase();
const OperationButton = useOperationButton();
const ViewButton = useViewButton();
const inspectionForm = ref<Partial<InspectionInfo>>({});
const state = reactive<{ id?: number; pageType: string }>({
  id: undefined,
  pageType: 'detail',
});
const init = (data: { id: number; pageType: string }) => {
  state.id = data.id;
  state.pageType = data.pageType;
  initWorkflow({ formKey: 'scm_operation_inspection', businessKey: data.id });
  getDetail();
};
const getDetail = async () => {
  if (state.id) {
    inspectionForm.value = await getInspectionDetailApi(state.id);
  }
};
const workflowSuccess = () => {
  emit('ok');
  closePopup();
};
const [registerPopup, { closePopup }] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" title="运营检查详情" height="100%" @register="registerPopup">
    <template #insertToolbar>
      <div v-if="state.pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton v-if="isProcessInstance" @success="workflowSuccess" />
      </div>
      <ViewButton v-if="isWorkflow && !isRunningTask && state.pageType === 'detail'" />
    </template>
    <BaseFilePreview :file-id="inspectionForm.fileId" />
  </BasicPopup>
</template>

<style></style>
