<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { InspectionInfo } from '#/api';

import { inject, ref, watch } from 'vue';

import { DETAIL_GRID_OPTIONS, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { useDictStore } from '@vben/stores';

import { cloneDeep } from 'lodash-es';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { inspectionFormKey } from '#/views/operation/inspection/manage/provideKey';

interface PurchaseSalesData {
  titleMonth1: string;
  titleMonth2: string;
  titleMonth3: string;
  data: Record<string, any>[];
}
const dictState = useDictStore();
const inspectionForm = inject(inspectionFormKey, ref<Partial<InspectionInfo>>({}));
const init = (val?: string) => {
  if (val) {
    const data = JSON.parse(val) as PurchaseSalesData;
    purchaseSalesData.value = data;
    // if (data.header.length > 0) {
    //   data.header.forEach((title, index) => {
    //     if (gridOptions.columns) {
    //       gridOptions.columns.push({
    //         title,
    //         field: `data${index}`,
    //         slots: { header: 'data_header', default: 'data_edit' },
    //       });
    //     }
    //   });
    //   gridApi.setGridOptions(gridOptions);
    // }
    if (data.data.length > 0) {
      gridApi.grid.loadData(data.data);
    }
  }
};
watch(() => inspectionForm.value.purchaseSalesData, init);
const purchaseSalesData = ref<PurchaseSalesData>({
  titleMonth1: '',
  titleMonth2: '',
  titleMonth3: '',
  data: [],
});
const baseColumns = [
  { title: '上游供应商', field: 'supplierName', slots: { default: 'supplierName' } },
  { title: '采购标的', field: 'purchaseTarget', slots: { default: 'purchaseTarget' } },
  { title: '', field: 'data1', slots: { header: 'data_header', default: 'data_edit' } },
  { title: '', field: 'data2', slots: { header: 'data_header', default: 'data_edit' } },
  { title: '', field: 'data3', slots: { header: 'data_header', default: 'data_edit' } },
  { title: '下游客户', field: 'customerName', slots: { default: 'customerName' } },
  { title: '本季度销售回款金额', field: 'salesPaymentAmount', slots: { default: 'salesPaymentAmount' } },
];
const gridOptions: VxeTableGridOptions = {
  columns: baseColumns,
  ...DETAIL_GRID_OPTIONS,
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});
const addRow = () => {
  gridApi.grid.insertAt({});
};
const save = async () => {
  const { visibleData } = gridApi.grid.getTableData();
  const data = cloneDeep(visibleData);
  data.forEach((item) => {
    delete item._X_ROW_KEY;
  });
  purchaseSalesData.value.data = data;
  return {
    purchaseSalesData: JSON.stringify(purchaseSalesData.value),
  };
};
defineExpose({ init, save });
</script>

<template>
  <div>
    <Grid table-title="采购销售情况（万元）">
      <template #toolbar-tools>
        <a-space>
          <!--<a-button type="primary" @click="addHeader">新增列</a-button>-->
          <a-button type="primary" @click="addRow">新增</a-button>
        </a-space>
      </template>
      <template #supplierName="{ row }">
        <a-input v-model:value="row.supplierName" />
      </template>
      <template #purchaseTarget="{ row }">
        <a-input v-model:value="row.purchaseTarget" />
      </template>
      <template #customerName="{ row }">
        <a-input v-model:value="row.customerName" />
      </template>
      <template #salesPaymentAmount="{ row }">
        <a-input v-model:value="row.salesPaymentAmount" />
      </template>
      <template #data_header="{ columnIndex }">
        <div class="flex items-center">
          <!-- 加1是因为接收参数是从1开始定义的，减2是因为前面有2列固定值 -->
          <a-input
            v-model:value="purchaseSalesData[`titleMonth${columnIndex + 1 - 2}` as keyof PurchaseSalesData]"
            class="w-full"
          />
          <!--<CloseCircleOutlined class="ml-2 cursor-pointer text-red-500" @click="removeHeader(columnIndex)" />-->
        </div>
      </template>
      <template #data_edit="{ row, column }">
        <a-input v-model:value="row[column.field]" />
      </template>
    </Grid>
    <a-form
      v-bind="{
        colon: false,
        ...FULL_FORM_ITEM_PROP,
      }"
    >
      <a-form-item label="各月份采购波动± 20%" name="purchaseFluctuate">
        <a-radio-group v-model:value="inspectionForm.purchaseFluctuate">
          <a-radio v-for="dict in dictState.getDictList('baseBooleanType')" :key="dict.value" :value="dict.value">
            {{ dict.label }}
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="情况描述" name="purchaseDetails">
        <a-textarea v-model:value="inspectionForm.purchaseDetails" :rows="4" />
      </a-form-item>
    </a-form>
  </div>
</template>

<style></style>
