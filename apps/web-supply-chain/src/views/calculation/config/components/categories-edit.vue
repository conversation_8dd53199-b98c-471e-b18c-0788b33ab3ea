<script setup lang="ts">
import type { ScoringCategoryBO } from '#/api';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { cloneDeep, defaultsDeep, uniqueId } from 'lodash-es';

let globalResolve: ((value: any) => void) | null = null;
let globalReject: ((reason?: unknown) => void) | null = null;
const FormRef = ref();
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    await FormRef.value.validate();
    if (globalResolve) {
      globalResolve(cloneDeep(formData.value));
    }
    globalResolve = null;
    globalReject = null;
    await modalApi.close();
  },
  onClosed: () => {
    globalReject && globalReject(new Error('取消编辑'));
    globalResolve = null;
    globalReject = null;
    formData.value = {};
  },
});
const rules = {
  categoryName: [{ required: true, message: '请输入分类名称' }],
  sortCode: [{ required: true, message: '请输入显示顺序' }],
};
const formData = ref<ScoringCategoryBO>({});
const modalTitle = computed(() => {
  return formData.value.id ? '编辑分类' : '新增分类';
});
const edit = (data: ScoringCategoryBO) => {
  return new Promise((resolve, reject) => {
    globalResolve = resolve;
    globalReject = reject;
    formData.value = defaultsDeep(data, {
      editKey: `categories_temp_${uniqueId()}`,
      sortCode: 0,
      editType: 'categories',
      indicators: [],
    });
    modalApi.open();
  });
};
defineExpose({
  edit,
});
</script>

<template>
  <Modal :title="modalTitle">
    <a-form ref="FormRef" :model="formData" :rules="rules">
      <a-form-item label="分类名称" name="categoryName">
        <a-input v-model:value="formData.categoryName" />
      </a-form-item>
      <a-form-item label="显示顺序" name="sortCode">
        <a-input-number v-model:value="formData.sortCode" :controls="false" :min="0" :precision="0" class="w-full" />
      </a-form-item>
    </a-form>
  </Modal>
</template>

<style></style>
