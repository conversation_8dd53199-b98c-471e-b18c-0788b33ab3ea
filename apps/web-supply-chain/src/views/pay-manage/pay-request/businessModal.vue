<script setup lang="ts">
import {computed, reactive, ref} from 'vue';

import {
  Button,
  Col,
  DatePicker,
  Form,
  FormItem,
  Input,
  Modal,
  Row,
  Select,
  Upload
} from 'ant-design-vue';
import {Page, type VbenFormProps} from '@vben/common-ui';
import { type EvaluateBaseInfo } from "#/api";
import { useVbenVxeGrid } from '#/adapter/vxe-table';

import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';

import {defaultsDeep, defineFormOptions} from '@vben/utils';

import type { Rule } from 'ant-design-vue/es/form';
import type {VxeTableGridOptions} from "@vben/plugins/vxe-table";

const emit = defineEmits(['confirm']);

const visible = ref(false);

const openModal = (data: boolean) => {
  console.log('open', data);
  visible.value = true;
};

const handleOk = () => {
  emit('confirm', detailForm);
  visible.value = false;
};

const handleCancel = () => {
  visible.value = false;
};
// 默认数据
const defaultForm: Partial<EvaluateBaseInfo> = {
  reportCode: "",
  reportName: "",
  projectId: 0,
  projectName: "",
  settlementDate: "",
  status: "",
  approvalStatus: "",
  remark: ""
};
let detailForm = reactive<Partial<EvaluateBaseInfo>>(defaultsDeep(defaultForm));

const colSpan = { md: 24, sm: 24 };
const labelCol = { style: { width: '150px' } };
const rules: Record<string, Rule[]> = {
  projectId: [{ required: true, message: '关联的项目名称', trigger: 'change' }],
  settlementDate: [{ required: true, message: ' 结清日期', trigger: 'change' }],
  reportName: [{ required: true, message: '项目后评价名称', trigger: 'change' }],
};
const executorCompanyOptions = [
  {
    label: '项目后评价名称1',
    value: '1',
  },
  {
    label: '项目后评价名称2',
    value: '2',
  }
];
const file = ref<any>();


const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'reportName',
      label: '采购订单编号',
    },
    {
      component: 'Input',
      fieldName: 'reportName',
      label: '采购订单名称',
    },
    {
      component: 'Input',
      fieldName: 'reportName',
      label: '商品类型',
    },
    {
      component: 'Input',
      fieldName: 'reportName',
      label: '商品名称',
    },
  ],
  showCollapseButton: true,
  submitOnEnter: true,
});

const gridOptions: VxeTableGridOptions = {
  data: detailForm.inboundReceiptItemBOs,
  props: {
    key: computed(() => detailForm.id || 'new')
  },
  columns: [
    { field: 'quantity', title: '采购订单编号', slots: { edit: 'edit_quantity' }, minWidth: '150px', },
    { field: 'sourceDocumentItemNumber', title: '采购订单名称', slots: { edit: 'edit_source_document_item_number' }, minWidth: '150px', },
    { field: 'sourceDocumentDisplay', title: '商品分类', slots: { edit: 'edit_source_document_display' }, minWidth: '150px', },
    { field: 'serialNumbers1', title: '商品编码', slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'serialNumbers2', title: '商品名称', slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'serialNumbers3', title: '规格型号', slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'serialNumbers4', title: '计量单位', slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'serialNumbers5', title: '数量', slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'serialNumbers6', title: '含税单价', slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'serialNumbers7', title: '含税金额', slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'serialNumbers8', title: '税额', slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'serialNumbers9', title: '税率(%)', slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'serialNumbers0', title: '采购订单行号', slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'remarks', title: '备注', slots: { edit: 'edit_remarks' }, minWidth: '200px' },
  ],
  editConfig: {
    trigger: 'click',
    mode: 'row',
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await operationSupervisePageApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

defineExpose({ openModal });
</script>

<template>
  <Modal v-model:visible="visible" title="业务数据明细" @ok="handleOk" @cancel="handleCancel" style="width: 1400px">
    <Page>
      <BasicCaption content="采购订单数据明细" />
      <div>
        <Grid>
          <template #edit_quantity="{ row }">
            <Input v-model:value="row.quantity" placeholder="采购订单编号" disabled/>
          </template>

          <template #edit_source_document_item_number="{ row }">
            <Input v-model:value="row.sourceDocumentItemNumber" placeholder="采购订单名称" disabled/>
          </template>

          <template #edit_source_document_display="{ row }">
            <Input v-model:value="row.sourceDocumentDisplay" placeholder="商品分类" disabled/>
          </template>

          <template #edit_serial_numbers="{ row }">
            <Input v-model:value="row.serialNumbers" placeholder="商品编码" />
          </template>

          <template #edit_source_document_display0="{ row }">
            <Input v-model:value="row.sourceDocumentDisplay" placeholder="商品名称" disabled/>
          </template>

          <template #edit_source_document_display1="{ row }">
            <Input v-model:value="row.sourceDocumentDisplay" placeholder="规格型号" disabled/>
          </template>

          <template #edit_source_document_display2="{ row }">
            <Input v-model:value="row.sourceDocumentDisplay" placeholder="计量单位" disabled/>
          </template>

          <template #edit_source_document_display3="{ row }">
            <Input v-model:value="row.sourceDocumentDisplay" placeholder="数量" disabled/>
          </template>

          <template #edit_source_document_display4="{ row }">
            <Input v-model:value="row.sourceDocumentDisplay" placeholder="含税单价" disabled/>
          </template>

          <template #edit_source_document_display5="{ row }">
            <Input v-model:value="row.sourceDocumentDisplay" placeholder="含税金额" disabled/>
          </template>

          <template #edit_source_document_display6="{ row }">
            <Input v-model:value="row.sourceDocumentDisplay" placeholder="税额" disabled/>
          </template>

          <template #edit_source_document_display7="{ row }">
            <Input v-model:value="row.sourceDocumentDisplay" placeholder="税率(%)" disabled/>
          </template>

          <template #edit_source_document_display8="{ row }">
            <Input v-model:value="row.sourceDocumentDisplay" placeholder="采购订单行号" disabled/>
          </template>

          <template #edit_remarks="{ row }">
            <Input v-model:value="row.remarks" placeholder="请输入备注" />
          </template>
        </Grid>
      </div>
    </Page>
  </Modal>
</template>

<style scoped></style>
