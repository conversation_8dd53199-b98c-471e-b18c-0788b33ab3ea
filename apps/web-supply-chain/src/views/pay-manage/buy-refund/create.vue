<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import { computed, reactive, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defaultsDeep } from '@vben/utils';

import { Col, DatePicker, Form, FormItem, Input, message, Row, Select, Textarea } from 'ant-design-vue';

// import { type AddInbound, type InboundReceiptItemBOs, type QueryGoodsRequest, addInboundApi, editInboundApi, inboundDetailApi, getPurchaseGoodsApi } from '#/api';

const emit = defineEmits(['register', 'ok']);

// 字典选项
const businessStructureOptions = ref([
  { label: '先采后销', value: '1' },
  { label: '先销后采', value: '2' },
]);

const projectModelOptions = ref([
  { label: '产业1', value: '1' },
  { label: '产业2', value: '2' },
  { label: '产业3', value: '3' },
  { label: '产业4', value: '4' },
  { label: '产业5', value: '5' },
]);

const { getDictList } = useDictStore();

// 默认数据
const defaultForm: Partial<AddInbound> = {
  id: undefined,
  createBy: 0,
  createTime: '',
  updateBy: 0,
  updateTime: '',
  version: 0,
  inboundReceiptCode: '',
  receiptDate:"",
  projectId: 0,
  projectName:  undefined,
  projectCode: '',
  warehouseId: 0,
  warehouseCode: '',
  warehouseName: '',
  customerCompanyCode: '',
  customerCompanyName: '',
  sourceDocumentType: '',
  deliveryReceiptId: 0,
  deliveryReceiptDisplay: '',
  amountWithTax: 0,
  invoicedAmountWithTax: 0,
  status: '',
  approvalStatus: '',
  invoiceStatus: '',
  remarks: '',
  isSnManaged: 0,
  inboundReceiptSourceRelBOS: [{
    sourceDocumentId: 123,
    sourceDocumentDisplay: '345',
    sourceDocumentType: '555'
  },
  ],
  inboundReceiptItemBOs: [],
};

let detailForm = reactive<Partial<AddInbound>>(defaultsDeep(defaultForm));

const colSpan = { md: 12, sm: 24 };

const rules: Record<string, Rule[]> = {
  // inboundReceiptCode: [{ required: true, message: '入库单编号', trigger: 'change' }],
  // projectName: [{ required: true, message: '所属项目名称', trigger: 'change' }],
  projectCode: [{ required: true, message: '所属项目编号', trigger: 'change' }],
  // sourceDocumentType: [{ required: true, message: '关联单据类型', trigger: 'change' }],
  // deliveryReceiptId: [{ required: true, message: '关联单据', trigger: 'change' }],
  // receiptDate: [{ required: true, message: '入库日期', trigger: 'change' }],
  // customerCompanyName: [{ required: true, message: '上/下游企业', trigger: 'change' }],
  // warehouseName: [{ required: true, message: '仓库名称', trigger: 'change' }],
};

const title = computed(() => {
  return detailForm.id ? '编辑项目' : '新增项目';
});

const init = async (data: any) => {
  if (data.id) {
    const res = await inboundDetailApi(data.id); // 调用真实 API

    // 深度复制确保响应性
    Object.keys(res).forEach(key => {
      detailForm[key] = res[key];
    });

    // 强制校验并转换inboundReceiptItemBOs字段
    if (!Array.isArray(res.inboundReceiptItemBOs) || res.inboundReceiptItemBOs === null) {
      detailForm.inboundReceiptItemBOs = [];
    } else {
      // 创建全新数组实例确保响应性
      detailForm.inboundReceiptItemBOs = [...res.inboundReceiptItemBOs];
    }

  } else {
    Object.assign(detailForm, defaultsDeep(defaultForm));
    detailForm.inboundReceiptItemBOs = defaultForm.inboundReceiptItemBOs ? [...defaultForm.inboundReceiptItemBOs] : [];
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);

const formRef = ref();
const save = async () => {
  try {
    await formRef.value.validate();

    changeOkLoading(true);
    // 显式类型断言确保类型正确
    const submitData = detailForm as Required<AddInbound>;

    // 根据是否存在id判断是新增还是编辑
    const res = detailForm.id
      ? await editInboundApi(submitData)
      : await addInboundApi(submitData);

    // 使用国际化提示保存成功
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } catch (error) {
    message.error('保存失败，请检查网络或输入内容');
    console.error('新增仓库失败:', error);
    changeOkLoading(false); // 防止 loading 卡住
  } finally {
    changeOkLoading(false);
  }
};

const labelCol = { style: { width: '150px' } };

const projectCodeOptions = [
  {
    value: '26',
    label: '项目1'
  },
  {
    value: '2',
    label: '项目2'
  },
  {
    value: '3',
    label: '项目3'
  },
  {
    value: '4',
    label: '项目4'
  }
]

// 创建响应式的查询参数
const purchaseGoodsParams = reactive<QueryGoodsRequest>({
  projectId: undefined,
  ids: []
});

const getPurchaseGoodsFn = async (value: QueryGoodsRequest) => {
  if (value.projectId) {
    try {
      const res = await getPurchaseGoodsApi(purchaseGoodsParams);
      if (res) {
        detailForm.inboundReceiptItemBOs = [...res];

        // 强制刷新表格
        if (gridApiLocation?.grid) {
          await gridApiLocation.grid.reloadData(detailForm.inboundReceiptItemBOs);
        }
      }
    } catch (error) {
      console.error('获取商品信息失败:', error);
      message.error('获取商品信息失败');
    }
  } else {
    // 如果没有选择任何项目，清空商品列表
    detailForm.inboundReceiptItemBOs = [];
    if (gridApiLocation?.grid) {
      await gridApiLocation.grid.reloadData([]);
    }
  }
}

const handleChangeProjectName = (selectedValues: string[]) => {
  // 清空之前的选择
  purchaseGoodsParams.ids = selectedValues;
  getPurchaseGoodsFn(purchaseGoodsParams)
};

const handleChangeProjectCode = (projectId: number) => {
  // 更新查询参数
  purchaseGoodsParams.projectId = projectId;
  getPurchaseGoodsFn(purchaseGoodsParams)
};

</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save">
    <Form
      ref="formRef"
      :colon="false"
      :model="detailForm"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <!-- 基本信息 -->
      <BasicCaption content="基本信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="采购退款记录编号" name="inboundReceiptCode">
            <Input v-model:value="detailForm.inboundReceiptCode" disabled/>
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="关联采购退货编号" name="inboundReceiptCode">
            <Select v-model:value="detailForm.inboundReceiptCode" :options="projectModelOptions"/>
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="所属项目名称" name="projectName">
            <Select
              v-model:value="detailForm.projectName"
              mode="multiple"
              :options="projectModelOptions"
              @change="handleChangeProjectName"
              placeholder="请选择所属项目名称"
            />
          </FormItem>
        </Col>

        <!-- 所属项目编号 -->
        <Col v-bind="colSpan">
          <FormItem label="所属项目编号" name="projectCode">
            <Select
              v-model:value="detailForm.projectCode"
              :options="projectCodeOptions"
              @change="handleChangeProjectCode"
              placeholder="请选择所属项目编号"
            />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="付款方企业" name="deliveryReceiptDisplay">
            <Select v-model:value="detailForm.deliveryReceiptDisplay" :options="businessStructureOptions" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="收款方企业" name="deliveryReceiptDisplay">
            <Select v-model:value="detailForm.deliveryReceiptDisplay" :options="businessStructureOptions" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="付款方社会统一信用代码" name="sourceDocumentType">
            <Input v-model:value="detailForm.sourceDocumentType" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="收款方社会统一信用代码" name="sourceDocumentType">
            <Input v-model:value="detailForm.sourceDocumentType" disabled />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="付款方银行账号" name="sourceDocumentType">
            <Input v-model:value="detailForm.sourceDocumentType" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="收款方银行账号" name="sourceDocumentType">
            <Input v-model:value="detailForm.sourceDocumentType" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="付款方开户行" name="sourceDocumentType">
            <Input v-model:value="detailForm.sourceDocumentType" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="收款方开户行" name="sourceDocumentType">
            <Input v-model:value="detailForm.sourceDocumentType" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="备注" name="remarks">
            <Textarea v-model:value="detailForm.remarks" :rows="3" />
          </FormItem>
        </Col>
      </Row>

      <!-- 收款信息 -->
      <BasicCaption content="收款信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="实际退款金额" name="deliveryReceiptDisplay">
            <Input v-model:value="detailForm.sourceDocumentType" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="实际收款方式" name="deliveryReceiptDisplay">
            <Select v-model:value="detailForm.sourceDocumentType" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="实际收款日期" name="inboundReceiptCode">
            <DatePicker
              v-model:value="detailForm.receiptDate"
              value-format="YYYY-MM-DD"
            />
          </FormItem>
        </Col>
      </Row>

      <Col v-bind="colSpan">
        <FormItem label="采购退货总金额" name="deliveryReceiptDisplay">
          {{ detailForm.sourceDocumentType }}
        </FormItem>
      </Col>

      <!-- 附件信息 -->
      <BasicCaption content="附件信息" />
    </Form>
  </BasicPopup>
</template>

<style scoped>
:where(.css-dev-only-do-not-override-1gaak89).ant-picker {
  width: 100%;
}
</style>
