<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { useVbenModal } from '@vben/common-ui';
import { defineFormOptions } from '@vben/utils';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getProductListApi } from '#/api';

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'productName',
      label: '商品名称',
    },
  ],
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-2',
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'categoryName', title: '商品分类' },
    { field: 'productName', title: '商品名称' },
    { field: 'productCode', title: '商品编码' },
    { field: 'productAlias', title: '商品别名' },
    { field: 'specifications', title: '规格型号' },
    { field: 'measureUnit', title: '单位' },
    { field: 'brandName', title: '材质' },
    { field: 'originName', title: '产地' },
  ],
  border: 'inner',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues: { spuCode: string; spuName: string; status: string }) => {
        return await getProductListApi({
          ...formValues,
          current: page.currentPage,
          size: page.pageSize,
        });
      },
    },
  },
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  separator: false,
});
const [Modal, modalApi] = useVbenModal({
  onOpened: () => {},
  onConfirm: async () => {
    // 获取表格中选中的商品
    const selectedRows = gridApi.grid.getCheckboxRecords();
    // 去掉每个对象中的 id 字段
    const processedRows = selectedRows.map(({ id: _id, ...rest }) => rest);
    modalApi.setData(processedRows);
    await modalApi.close();
  },
});
</script>

<template>
  <Modal title="商品选择" class="w-[1000px]">
    <Grid />
  </Modal>
</template>

<style></style>
