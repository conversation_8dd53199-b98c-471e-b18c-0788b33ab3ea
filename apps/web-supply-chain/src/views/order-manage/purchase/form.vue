<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { WorkflowStartInfo } from '@vben/types';

import type { OrderInfo } from '#/api';

import { computed, ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';

import { message, Select } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { BaseAttachmentList } from '#/adapter/base-ui';
import {
  addPurchaseListApi,
  BusinessStructureEnum,
  editPurchaseListApi,
  getOrderCodesApi,
  getProjectListApi,
  infoPurchaseListApi,
  infoSalesListApi,
} from '#/api';
import { ProjectSelector } from '#/components';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

import ProductInfo from './product-info.vue';

const emit = defineEmits(['register', 'ok']);
const { startWorkflow, WorkflowPreviewModal, initWorkflow } = useWorkflowBase();
const pageType = ref('edit');
const colSpan = { md: 12, sm: 24 };
const dictStore = useDictStore();

const orderInfoForm = ref<Partial<OrderInfo & WorkflowStartInfo>>({});
const formRef = ref();
const rules: Record<string, Rule[]> = {
  purchaseOrderName: [{ required: true, message: '请输入采购订单名称', trigger: 'blur' }],
  projectId: [{ required: true, message: '请选择所属项目名称', trigger: 'change' }],
  salesOrderId: [{ required: true, message: '请选择关联销售订单', trigger: 'change' }],
  supplierCompanyCode: [{ required: true, message: '请选择上游企业', trigger: 'change' }],
  businessDate: [{ required: true, message: '请选择业务日期', trigger: 'change' }],
  prepaymentRatio: [{ required: true, message: '请输入预付款比例', trigger: 'blur' }],
  // prepaymentAmount: [{ required: true, message: '请输入预付款金额', trigger: 'blur' }],
  // depositAmount: [{ required: true, message: '请输入保证金金额', trigger: 'blur' }],
  advanceRatio: [{ required: true, message: '请输入垫资比例', trigger: 'blur' }],
  paymentTermDays: [{ required: true, message: '请输入账期', trigger: 'blur' }],
};
const title = computed(() => {
  if (pageType.value === 'audit') {
    return '审批采购订单';
  }
  return orderInfoForm.value.id ? '编辑采购订单' : '新增采购订单';
});
const productGridRef = ref();
const init = async (data: OrderInfo & { pageType: string }) => {
  pageType.value = data.pageType;
  await initWorkflow({ formKey: 'scm_purchase_order', businessKey: data.id });
  if (data.id) {
    orderInfoForm.value = await infoPurchaseListApi({ id: data.id });
    productGridRef.value.setProductData(orderInfoForm.value.purchaseOrderItemVOS);
    const projectDetailList = await getProjectListApi({ projectCode: orderInfoForm.value.projectCode });
    if (projectDetailList && projectDetailList.length > 0) {
      supplierCompanyOptions.value = projectDetailList[0].projectPartners.filter(
        (item: any) => item.partnerType === 'SUPPLIER',
      );
    }
  } else {
    orderInfoForm.value = data;
    orderInfoForm.value.taskType = '0';
  }
};

// 处理项目选择事件的函数
const handleProjectSelect = async (_value: string, option: any) => {
  supplierCompanyOptions.value = option.projectPartners.filter((item: any) => item.partnerType === 'SUPPLIER');
  orderInfoForm.value.paymentTermDays = option.paymentTermDays;
  // orderInfoForm.value.projectId = option.id; // 项目ID
  orderInfoForm.value.projectName = option.projectName; // 项目编号
  orderInfoForm.value.projectCode = option.projectCode; // 项目编号
  orderInfoForm.value.executorCompanyName = option.executorCompanyName; // 贸易执行企业名称
  orderInfoForm.value.executorCompanyCode = option.executorCompanyCode; // 贸易执行企业代码
  // orderInfoForm.value.businessManagerId = option.businessManagerId; // 业务负责人ID
  // orderInfoForm.value.businessManagerName = option.businessManagerName; // 业务负责人名称
  // orderInfoForm.value.operationManagerId = option.operationManagerId; // 运营负责人ID
  // orderInfoForm.value.operationManagerName = option.operationManagerName; // 运营负责人名称
  // PURCHASE,SALE
  orderInfoForm.value.businessStructure = option.businessStructure; // 业务结构
  orderInfoForm.value.projectModel = option.projectModel; // 项目模式 (建材模式, 产业模式等)
  // const projectPartners = option.projectPartners || []; // 合作企业
  // companyOptions.value = projectPartners.filter((item) => item.partnerType === 'SUPPLIER') // 过滤上游企业
  orderInfoForm.value.businessManagerName = option.businessManager
    .map((v: { userName: string }) => v.userName)
    .join(',');
  orderInfoForm.value.operationManagerName = option.operationManager
    .map((v: { userName: string }) => v.userName)
    .join(',');
};
// 处理关联订单选择事件的函数
const handleCodesSelect = (_value: string, option: any) => {
  orderInfoForm.value.salesOrderId = option.value;
  orderInfoForm.value.salesOrderName = option.label;
  orderInfoForm.value.salesOrderCode = option.orderCode;
  // 选择关联订单后，查询订单详情接口获取商品信息传给商品表格
  orderInfoFun(option.value);
};

const orderInfoFun = async (id: number) => {
  const itemRecord = await infoSalesListApi({ id });
  // 去掉id字段
  let processedData = itemRecord.salesOrderItemVOS
    ? itemRecord.salesOrderItemVOS.map(({ id: _id, ...rest }) => rest)
    : [];
  if (orderInfoForm.value.id) {
    processedData = [...(orderInfoForm.value.purchaseOrderItemVOS || []), ...processedData];
  }
  productGridRef.value.setProductData(processedData);
};

const save = async (type: string) => {
  await formRef.value.validate();
  const productRecord = await productGridRef.value?.getProductData();
  if (!productRecord) {
    return;
  }
  if (productRecord.items?.length === 0) {
    message.error('商品信息不能为空！');
    return;
  }

  if (type === 'SUBMITTED') {
    const { processDefinitionKey, startUserSelectAssignees } = await startWorkflow();
    orderInfoForm.value.processDefinitionKey = processDefinitionKey;
    orderInfoForm.value.startUserSelectAssignees = startUserSelectAssignees;
  }

  const formData = cloneDeep(orderInfoForm.value);
  formData.purchaseOrderItemRequests = productRecord?.items;
  formData.totalAmountWithTax = productRecord.totalAmountWithTax;
  formData.totalTaxAmount = productRecord.totalTaxAmount;
  formData.totalAmountWithoutTax = productRecord.totalAmountWithoutTax;
  formData.status = type;

  changeOkLoading(true);
  let api = addPurchaseListApi;
  if (orderInfoForm.value.id) {
    api = editPurchaseListApi;
  }
  try {
    const res = await api(formData as OrderInfo);
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const businessStructureType = computed(() => {
  return dictStore.formatter(orderInfoForm.value.businessStructure, 'BUS_STRUCTURE') as string;
});
const supplierCompanyOptions = ref([]);
const handleCompanySelect = (_value: string, option: { companyCode: string; companyName: string }) => {
  orderInfoForm.value.supplierCompanyName = option.companyName;
};
</script>

<template>
  <BasicPopup v-bind="$attrs" :title="title" destroy-on-close @register="registerPopup">
    <template #insertToolbar>
      <a-space>
        <a-button v-if="pageType !== 'audit'" type="primary" @click="save('DRAFTING')">保存</a-button>
        <a-button v-if="pageType !== 'audit'" type="primary" @click="save('SUBMITTED')">提交</a-button>
      </a-space>
    </template>
    <a-form
      ref="formRef"
      :colon="false"
      :model="orderInfoForm"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
      class="px-8"
    >
      <a-row class="mt-5">
        <a-col v-bind="colSpan">
          <a-form-item label="订单编号" name="purchaseOrderCode">
            <a-input
              v-model:value="orderInfoForm.purchaseOrderCode"
              placeholder="留空自动生成"
              :disabled="orderInfoForm.id"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="订单名称" name="purchaseOrderName">
            <a-input v-model:value="orderInfoForm.purchaseOrderName" :disabled="pageType === 'audit'" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="项目编号" name="projectCode">
            <a-input v-model:value="orderInfoForm.projectCode" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="项目名称" name="projectId">
            <ProjectSelector
              v-model="orderInfoForm.projectId"
              :disabled="pageType === 'audit'"
              @change="handleProjectSelect"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="业务结构" name="businessStructure">
            <a-input v-model:value="businessStructureType" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="项目模式" name="projectModel">
            <a-input :value="dictStore.formatter(orderInfoForm.projectModel, 'PROJECT_MODE')" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan" v-if="orderInfoForm.businessStructure === BusinessStructureEnum.PURCHASE">
          <a-form-item label="销售订单编号" name="salesOrderCode">
            <a-input v-model:value="orderInfoForm.salesOrderCode" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan" v-if="orderInfoForm.businessStructure === BusinessStructureEnum.PURCHASE">
          <a-form-item label="关联销售订单" name="salesOrderId">
            <ApiComponent
              v-model="orderInfoForm.salesOrderId"
              :component="Select"
              :api="getOrderCodesApi"
              :params="{ orderType: 'SALES_ORDER', status: 'EFFECTIVE', projectId: orderInfoForm.projectId }"
              label-field="orderName"
              value-field="id"
              model-prop-name="value"
              show-search
              :filter-option="(input: string, option: any) => option.label.includes(input)"
              @change="handleCodesSelect"
              :disabled="pageType === 'audit'"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="贸易执行企业" name="executorCompanyName">
            <a-input v-model:value="orderInfoForm.executorCompanyName" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="上游企业" name="supplierCompanyCode">
            <a-select
              v-model:value="orderInfoForm.supplierCompanyCode"
              :options="supplierCompanyOptions"
              :disabled="pageType === 'audit'"
              show-search
              :filter-option="(input: string, option: any) => option.label.includes(input)"
              :field-names="{ label: 'companyName', value: 'companyCode' }"
              @change="handleCompanySelect"
            />
            <!--<ApiComponent-->
            <!--  v-model="orderInfoForm.supplierCompanyName"-->
            <!--  :component="Select"-->
            <!--  :api="getCompanyRecordApi"-->
            <!--  label-field="companyName"-->
            <!--  value-field="companyCode"-->
            <!--  model-prop-name="value"-->
            <!--  show-search-->
            <!--  :filter-option="(input: string, option: any) => option.label.includes(input)"-->
            <!--  @change="-->
            <!--    (_value: string, option: any) => {-->
            <!--      orderInfoForm.supplierCompanyName = option.label;-->
            <!--      orderInfoForm.supplierCompanyCode = option.value;-->
            <!--    }-->
            <!--  "-->
            <!--  :disabled="pageType === 'audit'"-->
            <!--/>-->
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="业务负责人" name="businessManagerName">
            <a-input v-model:value="orderInfoForm.businessManagerName" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="运营负责人" name="operationManagerName">
            <a-input v-model:value="orderInfoForm.operationManagerName" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="业务日期" name="businessDate">
            <a-date-picker
              v-model:value="orderInfoForm.businessDate"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
              :disabled="pageType === 'audit'"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="预计结束日期" name="estimatedEndDate">
            <a-date-picker
              v-model:value="orderInfoForm.estimatedEndDate"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
              :disabled="pageType === 'audit'"
            />
          </a-form-item>
        </a-col>
        <!--<a-col v-bind="colSpan" v-if="orderInfoForm.businessStructure === BusinessStructureEnum.SALE">-->
        <!--  <a-form-item label="账期（天)" name="paymentTermDays">-->
        <!--    <a-input-number-->
        <!--      v-model:value="orderInfoForm.paymentTermDays"-->
        <!--      class="w-full"-->
        <!--      :disabled="pageType === 'audit'"-->
        <!--    />-->
        <!--  </a-form-item>-->
        <!--</a-col>-->
        <a-col v-bind="colSpan">
          <a-form-item label="截止交货日期" name="deliveryDeadline">
            <a-date-picker
              v-model:value="orderInfoForm.deliveryDeadline"
              value-format="YYYY-MM-DD HH:mm:ss"
              class="w-full"
              :disabled="pageType === 'audit'"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="预付款金额" name="prepaymentAmount">
            <a-input-number
              v-model:value="orderInfoForm.prepaymentAmount"
              class="w-full"
              :controls="false"
              :disabled="pageType === 'audit'"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item
            label="保证金金额"
            name="depositAmount"
            :required="orderInfoForm.businessStructure === BusinessStructureEnum.SALE"
          >
            <a-input-number
              v-model:value="orderInfoForm.depositAmount"
              class="w-full"
              :controls="false"
              :disabled="pageType === 'audit'"
            />
          </a-form-item>
        </a-col>
        <!--<a-col v-bind="colSpan" v-if="orderInfoForm.businessStructure === BusinessStructureEnum.SALE">-->
        <!--  <a-form-item label="垫资比例(%)" name="advanceRatio">-->
        <!--    <a-input-number-->
        <!--      v-model:value="orderInfoForm.advanceRatio"-->
        <!--      class="w-full"-->
        <!--      :max="999999"-->
        <!--      :controls="false"-->
        <!--      :disabled="pageType === 'audit'"-->
        <!--    />-->
        <!--  </a-form-item>-->
        <!--</a-col>-->
        <a-col v-bind="colSpan">
          <a-form-item label="任务类型" name="taskType">
            <a-select
              v-model:value="orderInfoForm.taskType"
              :options="dictStore.getDictList('TASK_TYPE')"
              :disabled="pageType === 'audit'"
            />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注" name="remarks" v-bind="{ labelCol: { span: 3 }, wrapperCol: { span: 21 } }">
            <a-textarea v-model:value="orderInfoForm.remarks" :rows="4" :disabled="pageType === 'audit'" />
          </a-form-item>
        </a-col>
      </a-row>
      <!-- 商品信息 -->
      <BasicCaption content="商品信息" />
      <ProductInfo
        ref="productGridRef"
        :form-props="{ ...orderInfoForm, businessStructureType }"
        @order-info-fun="orderInfoFun"
        :page-type="pageType"
      />
      <BaseAttachmentList
        border="inner"
        :key="orderInfoForm.id"
        v-model="orderInfoForm.attachmentList"
        :business-id="orderInfoForm.id"
        business-type="SCM_PURCHASE_ORDER"
        :edit-mode="pageType !== 'audit'"
      />
    </a-form>
    <WorkflowPreviewModal v-if="pageType === 'edit'" />
  </BasicPopup>
</template>
