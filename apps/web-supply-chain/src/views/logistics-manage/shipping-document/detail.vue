<script setup lang="ts">
import type { GridApi, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { ShipmentBaseInfo } from '#/api';

import { nextTick, reactive, ref, watch } from 'vue';

import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { cloneDeep, formatDate } from '@vben/utils';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { Descriptions, DescriptionsItem } from 'ant-design-vue';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { detailShipmentApi, getCompanyApi, projectManageListApi } from '#/api';

const emit = defineEmits(['register', 'ok']);

const { getDictList } = useDictStore();

interface SelectOption {
  projectName?: string;
  id?: number | string;
  projectCode?: string;
  executorCompanyName?: string;
}

const belongProjectOptions = ref<SelectOption[]>([]);

// 默认数据
const defaultForm: Partial<ShipmentBaseInfo> = {
  id: undefined,
  createTime: undefined,
  createBy: undefined,
  updateTime: undefined,
  updateBy: undefined,
  version: undefined,
  shipmentCode: undefined,
  projectId: undefined,
  projectName: undefined,
  projectCode: undefined,
  shipmentType: undefined,
  transportMethod: undefined,
  carrierCompanyCode: undefined,
  carrierCompanyName: undefined,
  consigneeCompanyCode: undefined,
  consigneeCompanyName: undefined,
  billingCompanyCode: undefined,
  billingCompanyName: undefined,
  shipmentDate: undefined,
  status: undefined,
  totalShipmentCost: undefined,
  province: undefined,
  city: undefined,
  district: undefined,
  detailAddress: undefined,
  plannedDeliveryDate: undefined,
  receiptDistrict: undefined,
  receiptDetailAddress: undefined,
  receiptProvince: undefined,
  receiptCity: undefined,
  remarks: undefined,
  shipmentDeliveryList: [],
  shipmentItemList: [],
  shipmentSourceRelList: [],
  attachmentList: [],
  documentType: undefined,
};
let detailForm = reactive<Partial<ShipmentBaseInfo>>(cloneDeep(defaultForm));

const companyOptions = ref([]);
// 获取企业列表
const getCompanyList = async () => {
  const res = await getCompanyApi();
  Object.assign(companyOptions.value, res);
};

const title = '详情';

const init = async (data: any) => {
  const res = await projectManageListApi({ projectName: '' });
  belongProjectOptions.value = res.map((item) => ({
    projectName: item.projectName,
    id: item.id,
  }));
  await getCompanyList();
  if (data.id) {
    const res = await detailShipmentApi(data.id);
    Object.assign(detailForm, res);

    // 强制刷新表格数据
    setTimeout(() => {
      gridApi.grid.loadData(detailForm.shipmentDeliveryList || []);
      gridApiGoods.grid.loadData(detailForm.shipmentItemList || []);
    }, 0);
  } else {
    detailForm = cloneDeep(defaultForm);
    // 清空表格数据
    setTimeout(() => {
      gridApi.grid.loadData([]);
      gridApiGoods.grid.loadData([]);
    }, 0);
  }
};

const [registerPopup] = usePopupInner(init);

const grid: VxeTableGridOptions = {
  data: detailForm.shipmentDeliveryList,
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'deliveryType',
      title: '运载工具',
      slots: { default: 'deliveryType' },
      minWidth: '150px',
    },
    {
      field: 'deliveryNumber',
      title: '车辆号/船舶号/物流号',
      slots: { default: 'deliveryNumber' },
      minWidth: '160px',
    },
    {
      field: 'contactName',
      title: '联系人姓名',
      slots: { default: 'contactName' },
      minWidth: '150px',
    },
    {
      field: 'contactPhone',
      title: '联系人电话',
      slots: { default: 'contactPhone' },
      minWidth: '150px',
    },
    { field: 'remarks', title: '备注', slots: { default: 'remarks' }, minWidth: '200px' },
  ],
  editConfig: {
    trigger: 'click',
    mode: 'row',
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar_location_tools',
    },
  },
};

const gridGoods: VxeTableGridOptions = {
  data: detailForm.shipmentItemList,
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'productName',
      title: '商品名称',
      slots: { default: 'productName' },
      minWidth: '150px',
    },
    {
      field: 'productAlias',
      title: '商品别名',
      slots: { default: 'productAlias' },
      minWidth: '150px',
    },
    {
      field: 'productCode',
      title: '商品编码',
      slots: { default: 'productCode' },
      minWidth: '150px',
    },
    {
      field: 'specifications',
      title: '规格型号',
      slots: { default: 'specifications' },
      minWidth: '150px',
    },
    {
      field: 'measureUnit',
      title: '计量单位',
      slots: { default: 'measureUnit' },
      minWidth: '150px',
    },
    {
      field: 'brandName',
      title: '商品品牌',
      slots: { default: 'brandName' },
      minWidth: '150px',
    },
    {
      field: 'originName',
      title: '生产厂家',
      slots: { default: 'originName' },
      minWidth: '150px',
    },
    {
      field: 'shippedQuantity',
      title: '本次发运重量',
      slots: { default: 'shippedQuantity' },
      minWidth: '150px',
    },
    {
      field: 'sourceDocumentItemNumber',
      title: '源单据商品行号',
      slots: { default: 'sourceDocumentItemNumber' },
      minWidth: '150px',
    },
    {
      field: 'sourceDocumentName',
      title: '源单据名称',
      slots: { default: 'sourceDocumentName' },
      minWidth: '150px',
    },
    {
      field: 'sourceDocumentCode',
      title: '源单据编号',
      slots: { default: 'sourceDocumentCode' },
      minWidth: '150px',
    },
    { field: 'remarks', title: '备注', slots: { default: 'remarks' }, minWidth: '200px' },
  ],
  editConfig: {
    trigger: 'click',
    mode: 'row',
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar_location_tools',
    },
  },
};

const selectedProjectCode = ref<string>('');
const selectedExecutorCompanyName = ref<string>('');

const close = () => {
  Object.assign(detailForm, cloneDeep(defaultForm));
};

// 注册表格
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: grid,
});

const [GridGoods, gridApiGoods] = useVbenVxeGrid({
  gridOptions: gridGoods,
});

// 监听 shipmentDeliveryList 变化并更新表格
watch(
  () => detailForm.shipmentDeliveryList,
  (newVal) => {
    if (gridApi.grid) {
      nextTick(() => {
        gridApi.grid.loadData(newVal || []);
      });
    }
  },
  { deep: true },
);

// 监听 shipmentItemList 变化并更新表格
watch(
  () => detailForm.shipmentItemList,
  (newVal) => {
    if (gridApiGoods.grid) {
      nextTick(() => {
        gridApiGoods.grid.loadData(newVal || []);
      });
    }
  },
  { deep: true },
);

// 处理企业code转换为名称显示
const getCompanyLabel = (companyList: any[], code: string | undefined) => {
  if (!code) return '';
  const item = companyList.find((item) => item.companyCode === code);
  return item ? item.companyName : code;
};
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save" @close="close">
    <div :class="BASE_PAGE_CLASS_NAME">
      <Descriptions v-bind="DESCRIPTIONS_PROP">
        <DescriptionsItem label="发货单编号">
          <span v-if="detailForm.id">
            {{ detailForm.shipmentCode }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>

        <DescriptionsItem label="运输企业">
          <span v-if="detailForm.carrierCompanyCode">
            {{ getCompanyLabel(companyOptions, detailForm.carrierCompanyCode) }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>

        <DescriptionsItem label="运输方式">
          <span v-if="detailForm.transportMethod">
            {{ getDictList('TRANSPORT_MODE').find((item) => item.value === detailForm.transportMethod)?.label }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>

        <DescriptionsItem label="运输类型">
          <span v-if="detailForm.shipmentType">
            {{ getDictList('TRANSPORT_TYPE').find((item) => item.value === detailForm.shipmentType)?.label }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>

        <DescriptionsItem label="所属项目名称">
          <span v-if="detailForm.projectName">
            {{ detailForm.projectName }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>

        <DescriptionsItem label="所属项目编号">
          <span v-if="detailForm.projectCode">
            {{ detailForm.projectCode }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>

        <DescriptionsItem label="发运日期">
          <span v-if="detailForm.shipmentDate">
            {{ formatDate(detailForm.shipmentDate, 'YYYY-MM-DD') }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>

        <DescriptionsItem label="预计收货日期">
          <span v-if="detailForm.plannedDeliveryDate">
            {{ formatDate(detailForm.plannedDeliveryDate, 'YYYY-MM-DD') }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>

        <DescriptionsItem label="关联单据类型" name="documentType">
          <span v-if="detailForm.documentType">
            {{ getDictList('DOCUMENT_TYPE').find((item) => item.value === detailForm.documentType)?.label }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>

        <DescriptionsItem label="关联单据" name="shipmentSourceRelList">
          <span v-if="detailForm.shipmentSourceRelList && detailForm.shipmentSourceRelList.length > 0">
            {{ detailForm.shipmentSourceRelList.map((item: any) => item.sourceDocumentName).join('、') }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>

        <DescriptionsItem label="收货企业">
          <span v-if="detailForm.consigneeCompanyCode">
            {{ getCompanyLabel(companyOptions, detailForm.consigneeCompanyCode?.toString()) }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>

        <DescriptionsItem label="结算公司">
          <span v-if="detailForm.billingCompanyCode">
            {{ getCompanyLabel(companyOptions, detailForm.billingCompanyCode) }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>

        <DescriptionsItem label="贸易执行企业" name="executorCompanyName">
          <span v-if="detailForm.executorCompanyName">
            {{ detailForm.executorCompanyName }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>

        <DescriptionsItem label="发货地址">
          <span v-if="detailForm.province || detailForm.city || detailForm.district || detailForm.detailAddress">
            {{ detailForm.province }} / {{ detailForm.city }} / {{ detailForm.district }}
            {{ detailForm.detailAddress }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>

        <DescriptionsItem label="收货地址">
          <span
            v-if="
              detailForm.receiptProvince ||
              detailForm.receiptCity ||
              detailForm.receiptDistrict ||
              detailForm.receiptDetailAddress
            "
          >
            {{ detailForm.receiptProvince }} / {{ detailForm.receiptCity }} / {{ detailForm.receiptDistrict }}
            {{ detailForm.receiptDetailAddress }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>

        <DescriptionsItem label="备注">
          <span v-if="detailForm.remarks">
            {{ detailForm.remarks }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
      </Descriptions>

      <!-- 物流运输信息 -->
      <BasicCaption content="物流运输信息" />
      <div>
        <Grid>
          <template #deliveryType="{ row }">
            {{ getDictList('TRANSPORT_VEHICLE').find((item) => item.value === row.deliveryType)?.label }}
          </template>

          <template #deliveryNumber="{ row }">
            {{ row.deliveryNumber }}
          </template>

          <template #contactName="{ row }">
            {{ row.contactName }}
          </template>

          <template #contactPhone="{ row }">
            {{ row.contactPhone }}
          </template>

          <template #remarks="{ row }">
            {{ row.remarks }}
          </template>
        </Grid>
      </div>

      <!-- 商品信息 -->
      <BasicCaption content="商品信息" />
      <div>
        <GridGoods>
          <template #productName="{ row }">
            {{ row.productName }}
          </template>

          <template #productAlias="{ row }">
            {{ row.productAlias }}
          </template>

          <template #productCode="{ row }">
            {{ row.productCode }}
          </template>

          <template #specifications="{ row }">
            {{ row.specifications }}
          </template>

          <template #measureUnit="{ row }">
            {{ row.measureUnit }}
          </template>

          <template #brandName="{ row }"> {{ row.brandName }} </template>

          <template #originName="{ row }"> {{ row.originName }} </template>

          <template #shippedQuantity="{ row }">
            {{ row.shippedQuantity }}
          </template>

          <template #sourceDocumentItemNumber="{ row }">
            {{ row.sourceDocumentItemNumber }}
          </template>

          <template #sourceDocumentName="{ row }">
            {{ row.sourceDocumentName }}
          </template>

          <template #sourceDocumentCode="{ row }">
            {{ row.sourceDocumentCode }}
          </template>

          <template #remarks="{ row }">
            {{ row.remarks }}
          </template>
        </GridGoods>
      </div>

      <!-- 附件信息 -->
      <BaseAttachmentList
        v-model="detailForm.attachmentList"
        :business-id="detailForm.id"
        business-type="SCM_SHIPMENT"
      />
    </div>
  </BasicPopup>
</template>

<style scoped>
:deep(.ant-picker) {
  width: 100%;
}

:deep(.fe-basic-caption-border) {
  display: none;
}
</style>
