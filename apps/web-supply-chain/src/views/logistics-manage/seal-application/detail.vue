<script setup lang="ts">
import { getOrgListApi, getUser<PERSON>ist<PERSON><PERSON>, type ProjectBaseInfo, type SealBaseInfo } from '#/api';

import { reactive, ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import { cloneDeep, formatDate } from '@vben/utils';

import { Descriptions, DescriptionsItem } from 'ant-design-vue';
import { getDictLabel, getMultiDictLabels } from '#/utils/data-process';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { detailSealApplyApi, getCompanyApi, projectManageListApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';
import { useDictStore } from '@vben/stores';

const emit = defineEmits(['register', 'ok']);

const { useViewButton, initWorkflow, isWorkflow } = useWorkflowBase();
const pageType = ref('detail');
const ViewButton = useViewButton();
const belongProjectOptions = ref<ProjectBaseInfo[]>([]);

const { getDictList } = useDictStore();
const loading = reactive({
  submit: false,
});

// 默认数据
const defaultForm: Partial<SealBaseInfo> = {
  id: undefined,
  applyCode: undefined,
  userIds: [],
  userName: undefined,
  organId: undefined,
  organName: undefined,
  usageDate: undefined,
  status: undefined,
  approvalStatus: undefined,
  description: undefined,
  projectId: undefined,
  projectName: undefined,
  projectCode: undefined,
  sealType: undefined,
  documentName: undefined,
  documentType: undefined,
  copies: undefined,
  version: undefined,
  beginDate: '',
  endDate: '',
  attachmentList: [],
  processDefinitionKey: undefined,
  startUserSelectAssignees: {},
  variables: {},
  summary: [],
};

const detailForm = reactive<Partial<SealBaseInfo>>(cloneDeep(defaultForm));

const colSpan = { md: 12, sm: 24 };

const userOptions = ref([]);
const organOptions = ref([]);
// 获取用户列表
const getUserList = async () => {
  const res = await getUserListApi();
  Object.assign(userOptions.value, res);
};
// 获取部门列表
const getOrganList = async () => {
  const res = await getOrgListApi({ parentId: 1 });
  Object.assign(organOptions.value, res);
};
const title = '详情';

const init = async (data: any) => {
  await getUserList();
  await getOrganList();
  pageType.value = data.pageType;
  await initWorkflow({ formKey: 'scm_seal_apply', businessKey: data.id });
  const res = await projectManageListApi({});
  belongProjectOptions.value = res
    .filter((item) => item.id !== undefined) // 过滤掉 id 为 undefined 的项
    .map((item) => ({
      projectName: item.projectName,
      id: item.id as number | string, // 类型断言
    }));
  if (data.id) {
    const res = await detailSealApplyApi(data.id);
    Object.assign(detailForm, cloneDeep(res));
  } else {
    Object.assign(detailForm, cloneDeep(defaultForm));
  }
};

const [registerPopup] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" :title="title" @register="registerPopup">
    <template #insertToolbar>
      <ViewButton v-if="isWorkflow && pageType === 'detail'" />
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <Descriptions v-bind="DESCRIPTIONS_PROP">
        <DescriptionsItem label="申请部门">
          {{ organOptions.find((item) => item.id === detailForm.organId)?.name || '-' }}
        </DescriptionsItem>

        <DescriptionsItem label="申请人">
          {{
            detailForm.userIds && detailForm.userIds.length > 0
              ? detailForm.userIds.map((id) => userOptions.find((user) => user.id === id)?.realName || id).join(', ')
              : '-'
          }}
        </DescriptionsItem>

        <DescriptionsItem label="用印日期">
          {{ formatDate(detailForm.usageDate, 'YYYY-MM-DD') }}
        </DescriptionsItem>

        <DescriptionsItem label="用印份数">
          {{ detailForm.copies }}
        </DescriptionsItem>

        <DescriptionsItem label="用印项目">
          {{ detailForm.projectName }}
        </DescriptionsItem>

        <DescriptionsItem label="印章种类">
          {{ getDictLabel(getDictList('SEAL_APPLY_TYPE'), detailForm.sealType as string) }}
        </DescriptionsItem>

        <DescriptionsItem label="用印事别">
          {{ detailForm.description }}
        </DescriptionsItem>

        <DescriptionsItem label="文件类型">
          {{
            detailForm.documentType ? getMultiDictLabels(getDictList('SEAL_DOC_TYPE'), detailForm.documentType) : '-'
          }}
        </DescriptionsItem>

        <DescriptionsItem label="文件名称">
          {{ detailForm.documentName ? detailForm.documentName : '-' }}
        </DescriptionsItem>
      </Descriptions>

      <!--   附件信息   -->
      <BaseAttachmentList
        border="inner"
        v-model="detailForm.attachmentList"
        :business-id="detailForm.id"
        business-type="SCM_SEAL_APPLY"
      />
    </div>
  </BasicPopup>
</template>

<style scoped></style>
