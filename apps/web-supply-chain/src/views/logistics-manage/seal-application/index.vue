<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import {
  type SealBaseInfo,
  type ProjectBaseInfo,
  getOrgListApi,
  projectManageListApi,
  delSealApplyApi,
  getUserListApi,
  sealApplyPageApi,
} from '#/api';

import { onMounted, ref, watch } from 'vue';

import { useModalUrl } from '@vben/base-ui';
import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Modal as AntdModal, Button, message, Space, TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

import Create from './create.vue';
import Detail from './detail.vue';

const { getDictList } = useDictStore();
const sortKey = ref<string>('create_time');
interface UserInfo {
  userId: string;
  userName: string;
  realName?: string;
}

interface OrgInfo {
  id: number;
  name: string;
}
const belongProjectOptions = ref<ProjectBaseInfo[]>([]);
const userOptions = ref<UserInfo[]>([]);
const organOptions = ref<OrgInfo[]>([]);
const dataLoaded = ref(false);
// 获取用户列表
const getUserList = async () => {
  try {
    const res = await getUserListApi();
    userOptions.value = res || [];
  } catch (error) {
    console.error('获取用户列表失败:', error);
    userOptions.value = [];
  }
};
// 获取部门列表
const getOrganList = async () => {
  try {
    const res = await getOrgListApi({ parentId: 1 });
    organOptions.value = res || [];
  } catch (error) {
    console.error('获取部门列表失败:', error);
    organOptions.value = [];
  }
};
// 获取项目列表
const getProjectList = async () => {
  try {
    const res = await projectManageListApi({});
    belongProjectOptions.value = res || [];
  } catch (error) {
    console.error('获取部门列表失败:', error);
    organOptions.value = [];
  }
};

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Select',
      fieldName: 'sealType',
      label: '印章种类',
      componentProps: {
        options: getDictList('SEAL_APPLY_TYPE'),
      },
    },
    {
      component: 'Input',
      fieldName: 'projectCode',
      label: '项目编号',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Select',
      fieldName: 'organId',
      label: '申请部门',
      componentProps: {
        options: organOptions.value,
        fieldNames: { label: 'name', value: 'id' },
        allowClear: true,
        showSearch: true,
        optionFilterProp: 'name',
      },
    },
    {
      component: 'Select',
      fieldName: 'userId',
      label: '申请人',
      componentProps: {
        options: userOptions.value,
        fieldNames: { label: 'userName', value: 'userId' },
        allowClear: true,
        showSearch: true,
        optionFilterProp: 'userName',
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      componentProps: {
        options: getDictList('BUS_STATUS'),
      },
    },
    {
      component: 'Select',
      fieldName: 'approvalStatus',
      label: '审批状态',
      componentProps: {
        options: getDictList('REVIEW_STATUS'),
      },
    },
    {
      component: 'DateRangePicker',
      fieldName: 'usageDate',
      label: '用印日期',
    },
  ],
  fieldMappingTime: [['usageDate', ['beginDate', 'endDate'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']]],
  showCollapseButton: true,
  submitOnEnter: true,
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    {
      field: 'sealType',
      title: '印章种类',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'SEAL_APPLY_TYPE',
        },
      },
    },
    { field: 'projectCode', title: '项目编号' },
    {
      field: 'projectName',
      title: '项目名称',
    },
    {
      field: 'organId',
      title: '申请部门',
      formatter: ({ cellValue }) => {
        // 确保 organOptions 已加载且有数据
        if (!organOptions.value || organOptions.value.length === 0) {
          return cellValue || '';
        }

        // 如果 cellValue 是数字类型，则直接查找对应的部门名称
        if (typeof cellValue === 'number') {
          const dictItem = organOptions.value.find((item) => item.id === cellValue);
          return dictItem ? dictItem.name : cellValue;
        }

        // 如果 cellValue 是字符串类型（可能包含多个ID），按逗号分割并处理
        if (typeof cellValue === 'string') {
          const values = cellValue.split(',').filter((item) => item.trim() !== '');
          return values
            .map((value) => {
              const dictItem = organOptions.value.find((item) => item.id === Number(value));
              return dictItem ? dictItem.name : value;
            })
            .join(', ');
        }

        // 其他情况返回空字符串
        return '';
      },
    },
    {
      field: 'userIds',
      title: '申请人',
      formatter: ({ cellValue }) => {
        if (!userOptions.value || userOptions.value.length === 0) {
          return cellValue || '';
        }

        const userNames = cellValue
          .map((id: number) => {
            const user = userOptions.value.find((u) => u.id === id);
            return user ? user.realName || user.userName : id;
          })
          .filter((name) => name);

        return userNames.join(', ');
      },
    },
    { field: 'usageDate', title: '用印日期', formatter: 'formatDate' },
    {
      field: 'status',
      title: '状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'BUS_STATUS',
        },
      },
    },
    {
      field: 'approvalStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'REVIEW_STATUS',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await sealApplyPageApi({
          current: page.currentPage,
          size: page.pageSize,
          descs: sortKey.value,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

watch(dataLoaded, (loaded) => {
  if (loaded) {
    // 触发表格重新渲染，确保使用最新的选项数据
    gridApi?.reload?.();
  }
});

const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 创建
const add = () => {
  openFormPopup(true, { pageType: 'edit' });
};

// 编辑
const handleEdit = (row: SealBaseInfo) => {
  openFormPopup(true, { ...row, pageType: 'edit' });
};

// 查看
const handleDetail = (row: SealBaseInfo) => {
  openDetailPopup(true, { ...row, pageType: 'detail' });
};

// 删除
const del = (row: SealBaseInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      await delSealApplyApi(row.id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};

const access = (row: { id: number }) => {
  openFormPopup(true, { ...row, pageType: 'edit' });
};
const viewDetail = (row: { id: number }) => {
  openDetailPopup(true, { ...row, pageType: 'detail' });
};
const audit = (row: { id: number }) => {
  openFormPopup(true, { ...row, pageType: 'audit' });
};

useModalUrl({
  handlers: {
    // 编辑弹层
    edit: (params) => {
      const data: AccessInfo = {
        id: params.id,
      };
      access(data);
    },

    // 详情弹层
    detail: (params) => {
      const data: AccessInfo = {
        id: params.id,
      };
      viewDetail(data);
    },

    // 审核弹层
    audit: (params) => {
      const data: AccessInfo = {
        id: params.id,
        pageType: 'audit',
      };
      audit(data);
    },
  },
});

const editSuccess = () => {
  gridApi.formApi.submitForm();
};

onMounted(async () => {
  try {
    await Promise.all([getUserList(), getOrganList(), getProjectList()]);
    dataLoaded.value = true;
  } catch (error) {
    console.error('数据初始化失败:', error);
    message.error('数据加载失败');
  }
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="add">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.add') }}
        </Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink
            v-if="row.status === 'DRAFTING' || row.approvalStatus === 'REJECTED'"
            @click="handleEdit(row)"
          >
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink @click="handleDetail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
          <TypographyLink v-if="row.status === 'DRAFTING'" type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Create @register="registerForm" @ok="editSuccess" />
    <Detail @register="registerDetail" />
  </Page>
</template>

<style></style>
