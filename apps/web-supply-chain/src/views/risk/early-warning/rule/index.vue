<script setup lang="ts">
import type { EarlyWarningRuleInfo } from '#/api';

import { ref } from 'vue';

import { StatusTag } from '@vben/base-ui';
import { Page, useVbenModal } from '@vben/common-ui';

import { Flex } from 'ant-design-vue';

import { editEarlyWarningRuleApi, getEarlyWarningRuleListApi } from '#/api';
import RuleForm from '#/views/risk/early-warning/rule/components/rule-form.vue';
import TriggerConditionItem from '#/views/risk/early-warning/rule/components/trigger-condition-item.vue';

const ruleList = ref<(EarlyWarningRuleInfo & { loading?: boolean })[]>([]);
const getRuleList = async () => {
  ruleList.value = await getEarlyWarningRuleListApi();
};
getRuleList();
const changeStatus = async (_checked: string, item: EarlyWarningRuleInfo & { loading?: boolean }) => {
  item.loading = true;
  try {
    await editEarlyWarningRuleApi(item);
  } catch (error) {
    await getRuleList();
    console.error(error);
  } finally {
    item.loading = false;
  }
};
const [Model, modalApi] = useVbenModal({
  connectedComponent: RuleForm,
  onOpenChange: (isOpen) => {
    if (!isOpen) {
      getRuleList();
    }
  },
});
const edit = (item: EarlyWarningRuleInfo) => {
  modalApi.setData(item).open();
};
</script>

<template>
  <Page auto-content-height>
    <Flex wrap="wrap" gap="middle">
      <a-card v-for="item in ruleList" :title="item.ruleName" :key="item.id" class="w-[400px]">
        <template #extra>
          <a-switch
            v-model:checked="item.status"
            checked-value="OPEN"
            un-checked-value="CLOSE"
            :loading="item.loading"
            @change="(checked: string) => changeStatus(checked, item)"
          />
        </template>
        <div class="flex min-h-[60px] flex-col justify-between">
          <div class="flex">
            <span class="font-bold">预警等级：</span><StatusTag code="RISK_LEVEL" :value="item.warningLevel" />
          </div>
          <TriggerConditionItem :item="item" />
        </div>
        <template #actions>
          <a-typography-link @click="edit(item)"> 规则调整 </a-typography-link>
        </template>
      </a-card>
    </Flex>
    <Model />
  </Page>
</template>

<style></style>
