<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { Warehouse_inventoryInfo } from '#/api';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getInventoryListApi } from '#/api';

import Detail from './detail.vue';

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'productName',
      label: '商品名称',
    },
    {
      component: 'Input',
      fieldName: 'productAlias',
      label: '商品别名',
    },
    {
      component: 'Input',
      fieldName: 'warehouseName',
      label: '仓库名称',
    },
    {
      component: 'Input',
      fieldName: 'warehouseName',
      label: '仓储运营方',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Input',
      fieldName: 'purchaseOrderName',
      label: '采购订单名称',
    },
    {
      component: 'Input',
      fieldName: 'supplierName',
      label: '上游企业',
    },
    {
      component: 'Input',
      fieldName: 'salesOrderName',
      label: '销售订单名称',
    },
    {
      component: 'Input',
      fieldName: 'purchaserName',
      label: '下游企业',
    },
    {
      component: 'DatePicker',
      fieldName: 'businessDate',
      label: '库存日期',
    },
  ],
  fieldMappingTime: [['businessDate', ['establishDateStart', 'establishDateEnd'], 'x']],
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  wrapperClass: 'grid-cols-1 md:grid-cols-3',
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'productName', title: '商品名称' },
    { field: 'productAlias', title: '商品别名' },
    {
      field: 'specifications',
      title: '规格型号',
    },
    {
      field: 'productCode',
      title: '商品编码',
    },
    { field: 'measureUnit', title: '计量单位' },
    { field: 'quantity', title: '库存重量' },
    { field: 'availableQuantity', title: '可用重量' },
    { field: 'warehouseName', title: '仓库名称' },
    { field: 'businessDate', title: '仓储运营方' },
    { field: 'projectName', title: '项目名称' },
    { field: 'projectCode', title: '项目编号' },
    { field: 'purchaseOrderName', title: '采购订单名称' },
    { field: 'purchaseOrderCode', title: '采购订单编号' },
    { field: 'salesCompanyName', title: '上游企业' },
    { field: 'salesOrderName', title: '销售订单名称' },
    { field: 'salesOrderCode', title: '销售订单编号' },
    { field: 'purchaseCompanyName', title: '下游企业' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getInventoryListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const detail = (row: Warehouse_inventoryInfo) => {
  openFormPopup(true, row);
};

const [registerForm, { openPopup: openFormPopup }] = usePopup();
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #action="{ row }">
        <a-space>
          <TypographyLink @click="detail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
        </a-space>
      </template>
    </Grid>
    <Detail @register="registerForm" />
  </Page>
</template>

<style></style>
