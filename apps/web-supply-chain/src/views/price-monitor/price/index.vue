<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { PriceProductInfo } from '#/api/price-monitor';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { message, TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { addPriceApi, getPriceListApi } from '#/api/price-monitor';

import PriceDetail from './priceDetail.vue';
import PriceModal from './priceModal.vue';
import ProductPopup from './product-popup.vue';
import TrendDetail from './trendDetail.vue';

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'categoryName',
      label: '分类名称',
    },
    {
      component: 'Input',
      fieldName: 'productName',
      label: '商品名称',
    },
    {
      component: 'Input',
      fieldName: 'specifications',
      label: '规格型号',
    },
  ],
  showCollapseButton: true,
  submitOnEnter: true,
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'categoryName', title: '分类名称' },
    { field: 'productName', title: '商品名称' },
    {
      field: 'specifications',
      title: '规格型号',
    },
    {
      field: 'price',
      title: '最新价格',
    },
    { field: 'priceDate', title: '最后录入时间' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getPriceListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const priceModalRef = ref();
const [trendDetail, { openPopup: openDetailPopup }] = usePopup();
const [priceDetail, { openPopup: openPricePopup }] = usePopup();
const [Modal, modalApi] = useVbenModal({
  connectedComponent: ProductPopup,
  onOpenChange: (isOpen) => {
    if (!isOpen) {
      gridApi.formApi.submitForm();
    }
  },
});
const toTrenddetail = (row: PriceProductInfo) => {
  openDetailPopup(true, row);
};
const toPriceDetail = (row: PriceProductInfo) => {
  openPricePopup(true, row);
};
const handleSubmit = async (formData: PriceProductInfo) => {
  await addPriceApi(formData);
  gridApi.formApi.submitForm();
  message.success($t('base.resSuccess'));
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-button class="mr-2" type="primary" @click="modalApi.setData({}).open()">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.Add') }}
        </a-button>
      </template>
      <template #action="{ row }">
        <a-space>
          <TypographyLink @click="priceModalRef.showModal(row)"> 新增价格 </TypographyLink>
          <TypographyLink @click="toTrenddetail(row)" type="danger"> 价格趋势 </TypographyLink>
          <TypographyLink @click="toPriceDetail(row)"> 价格明细 </TypographyLink>
        </a-space>
      </template>
    </Grid>
    <TrendDetail @register="trendDetail" />
    <PriceDetail @register="priceDetail" />
    <PriceModal ref="priceModalRef" @submit="handleSubmit" />
    <Modal />
  </Page>
</template>

<style></style>
