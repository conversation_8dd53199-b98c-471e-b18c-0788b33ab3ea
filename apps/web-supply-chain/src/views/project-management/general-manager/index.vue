<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { MeetingInfo, ProjectBaseInfo } from '#/api';

import { onMounted, reactive, ref } from 'vue';

import { useModalUrl } from '@vben/base-ui';
import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { message, Modal, Radio, RadioGroup, Space, Spin, TypographyLink } from 'ant-design-vue';
import { Modal as AntdModal } from 'ant-design-vue/es/components';
import { cloneDeep } from 'lodash-es';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  getCompanyApi,
  getUserListApi,
  projectGeneralCancelApi,
  projectGeneralPageApi,
  projectGeneralUploadSummaryApi,
} from '#/api';

import Detail from '../components/detail.vue';
import Edit from '../components/edit.vue';

const { getDictList } = useDictStore();

const sortKey = ref<string>('create_time');
const dataLoaded = ref(false); // 添加加载状态
const usersOptions = ref([]);
const companyOptions = ref([]);
// 获取用户列表
const getUserList = async () => {
  const res = await getUserListApi();
  Object.assign(usersOptions.value, res);
};
// 获取企业列表
const getCompanyList = async () => {
  const res = await getCompanyApi();
  Object.assign(companyOptions.value, res);
};
// 同时执行两个异步请求
const loadData = async () => {
  try {
    await Promise.all([getCompanyList(), getUserList()]);
  } finally {
    dataLoaded.value = true;
  }
};

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectCode',
      label: '项目编号',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    // {
    //   component: 'Select',
    //   fieldName: 'executorCompanyName',
    //   label: '贸易执行企业',
    //   componentProps: {
    //     options: companyOptions.value,
    //     fieldNames: { label: 'companyName', value: 'companyCode' },
    //     allowClear: true,
    //   },
    // },
    {
      component: 'Select',
      fieldName: 'businessStructure',
      label: '业务结构',
      componentProps: {
        options: getDictList('BUS_STRUCTURE'),
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'projectModel',
      label: '项目模式',
      componentProps: {
        options: getDictList('PROJECT_MODE'),
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'generalManagerOfficeStatus',
      label: '总经办会议状态',
      componentProps: {
        options: getDictList('PROJECT_REVIEW_STATUS'),
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'approvalStatus',
      label: '审批状态',
      componentProps: {
        options: getDictList('REVIEW_STATUS'),
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'uploadStatus',
      label: '会议纪要状态',
      componentProps: {
        options: getDictList('PROJECT_MEETING_STATUS'),
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'purchaseMode',
      label: '采购模式',
      componentProps: {
        options: getDictList('PURCHASE_MODE'),
        allowClear: true,
      },
    },
  ],
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    // { field: 'reviewNodeId', title: '总经办评审编号' },
    { field: 'projectCode', title: '项目编号' },
    { field: 'projectName', title: '项目名称' },
    {
      field: 'businessStructure',
      title: '业务结构',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'BUS_STRUCTURE',
        },
      },
    },
    {
      field: 'projectModel',
      title: '项目模式',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'PROJECT_MODE',
        },
      },
    },
    // { field: 'executorCompanyName', title: '贸易执行企业' },
    {
      field: 'purchaseMode',
      title: '采购模式',
      width: 150,
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'PURCHASE_MODE',
          displayMode: 'collapse',
        },
      },
    },
    {
      field: 'generalManagerOfficeStatus',
      title: '总经办会议状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'PROJECT_REVIEW_STATUS',
        },
      },
    },
    {
      field: 'approvalStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'REVIEW_STATUS',
        },
      },
    },
    {
      field: 'uploadStatus',
      title: '会议纪要状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'PROJECT_MEETING_STATUS',
        },
      },
    },
    {
      field: 'reviewType',
      title: '评审类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'PROJECT_REVIEW_TYPE',
        },
      },
    },
    {
      field: 'businessManager',
      title: '业务负责人',
      formatter: (managers) => {
        if (!managers.cellValue || !Array.isArray(managers.cellValue) || managers.cellValue.length === 0) {
          return '';
        }
        return managers.cellValue
          .map((manager) => manager.userName)
          .filter(Boolean)
          .join(', ');
      },
    },
    // { field: 'createName', title: '创建人' },
    { field: 'createTime', title: '创建日期', formatter: 'formatDate' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const processedFormValues = { ...formValues };

        // 如果 purchaseMode 是数组，则转换为逗号分隔的字符串
        // if (Array.isArray(processedFormValues.purchaseMode)) {
        //   processedFormValues.purchaseMode = processedFormValues.purchaseMode.join(',');
        // }

        return await projectGeneralPageApi({
          current: page.currentPage,
          size: page.pageSize,
          descs: sortKey.value,
          ...processedFormValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [registerEditForm, { openPopup: openEditPopup }] = usePopup();
const [registerDetailForm, { openPopup: openDetailPopup }] = usePopup();

const access = (row: { id: number }) => {
  openEditPopup(true, { ...row, projectType, pageType: 'edit' });
};
const viewDetail = (row: { id: number }) => {
  openDetailPopup(true, { ...row, projectType, pageType: 'detail' });
};
const audit = (row: { id: number }) => {
  openDetailPopup(true, { ...row, projectType, pageType: 'audit' });
};
useModalUrl({
  handlers: {
    // 编辑弹层
    edit: (params) => {
      const data = {
        id: params.id,
      };
      access(data);
    },

    // 详情弹层
    detail: (params) => {
      const data = {
        id: params.id,
      };
      viewDetail(data);
    },

    // 审核弹层
    audit: (params) => {
      const data = {
        id: params.id,
        formKey: params.formKey,
        pageType: 'audit',
      };
      audit(data);
    },
  },
});

const meetingForm = reactive<MeetingInfo>({
  reviewNodeId: undefined,
  attachmentList: [],
  reviewStatus: 'REVIEWED',
});

const companySelectorModal = reactive<{ selectRow: ProjectBaseInfo | undefined; visible: boolean }>({
  visible: false,
  selectRow: undefined,
});

const handleCompanySelectOk = async () => {
  if (!meetingForm.reviewStatus) {
    message.error('请选择上会结果');
    return;
  }
  if (companySelectorModal.selectRow && companySelectorModal.selectRow.reviewNodeId) {
    // meetingForm.reviewNodeId = companySelectorModal.selectRow.reviewNodeId;
    const formData = cloneDeep(meetingForm);
    formData.reviewNodeId = companySelectorModal.selectRow.reviewNodeId;
    await projectGeneralUploadSummaryApi(formData);
    companySelectorModal.visible = false;
    await gridApi.formApi.submitForm();
  }
};

const handleCompanySelectCancel = () => {
  companySelectorModal.visible = false;
};

const projectType = 'general';
const edit = (row: ProjectBaseInfo) => {
  const editRow = { ...row, projectType, pageType: 'edit' };
  openEditPopup(true, editRow);
};
const detail = (row: ProjectBaseInfo) => {
  const detailRow = { ...row, projectType, pageType: 'detail' };
  openDetailPopup(true, detailRow);
};
const meeting = (row: ProjectBaseInfo) => {
  companySelectorModal.visible = true;
  companySelectorModal.selectRow = row;
};
const editSuccess = () => {
  gridApi.formApi.submitForm();
};
const detailSuccess = () => {
  gridApi.formApi.submitForm();
};

const reject = async (row: ProjectBaseInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmRejectTitle'),
    content: $t('base.confirmRejectContent'),
    async onOk() {
      try {
        await projectGeneralCancelApi(row.reviewNodeId as number);
        message.success($t('base.resSuccess'));
        await gridApi.formApi.submitForm();
      } catch (error: any) {
        message.error(`驳回失败: ${error.message}`);
      }
    },
  });
};

onMounted(() => {
  loadData();
});
</script>

<template>
  <Page auto-content-height>
    <Grid v-if="dataLoaded">
      <template #action="{ row }">
        <Space>
          <TypographyLink v-if="row.generalManagerOfficeStatus === 'UNSUBMITTED'" @click="edit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink @click="detail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
          <TypographyLink type="danger" v-if="row.generalManagerOfficeStatus === 'UNSUBMITTED'" @click="reject(row)">
            {{ $t('base.reject') }}
          </TypographyLink>
          <TypographyLink
            v-if="row.generalManagerOfficeStatus === 'UNDER_REVIEW' && row.uploadStatus === 'PENDING_UPLOAD'"
            @click="meeting(row)"
          >
            {{ $t('base.meetingMinutes') }}
          </TypographyLink>
        </Space>
      </template>
    </Grid>
    <div v-else class="flex h-64 items-center justify-center">
      <Spin size="large" />
    </div>
    <Edit @register="registerEditForm" @ok="editSuccess" />
    <Detail @register="registerDetailForm" @ok="detailSuccess" />

    <Modal
      v-model:open="companySelectorModal.visible"
      title="上传会议纪要"
      @ok="handleCompanySelectOk"
      @cancel="handleCompanySelectCancel"
      width="800px"
    >
      <BaseAttachmentList
        v-model="meetingForm.attachmentList"
        :business-id="meetingForm.reviewNodeId"
        business-type="SCM_GENERAL_MANAGER_RECORD"
        edit-mode
      >
        <template #header> </template>
      </BaseAttachmentList>
      <div class="mx-4">
        <span style="color: red">*</span>上会结果：
        <RadioGroup v-model:value="meetingForm.reviewStatus">
          <Radio v-for="option in getDictList('SELECT_REVIEW_STATUS')" :key="option.value" :value="option.value">
            {{ option.label }}
          </Radio>
        </RadioGroup>
      </div>
    </Modal>
  </Page>
</template>

<style></style>
