import { defineComponent } from 'vue';

import { CloudDiskFilePicker } from '@vben/base-ui';

import { getDownloadFileLinkApi, getPreviewFileExternalLink, uploadFileApi } from '#/api';
import * as cloudDiskApi from '#/api/core/cloud-disk';

export const BaseCloudDiskFilePicker = defineComponent({
  ...CloudDiskFilePicker,
  props: {
    ...(CloudDiskFilePicker as any).props,
    apiSuite: {
      type: Object,
      ...(CloudDiskFilePicker as any).props.cloudDiskApiGroup,
      default: cloudDiskApi,
    },
    previewExternalApi: {
      type: Function,
      ...(CloudDiskFilePicker as any).props.previewExternalApi,
      default: getPreviewFileExternalLink,
    },
    downloadApi: {
      type: Function,
      ...(CloudDiskFilePicker as any).props.downloadApi,
      default: getDownloadFileLinkApi,
    },
    uploadApi: {
      type: Function,
      ...(CloudDiskFilePicker as any).props.uploadApi,
      default: uploadFileApi,
    },
  },
});
