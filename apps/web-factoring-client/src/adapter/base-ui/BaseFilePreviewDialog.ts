import { defineComponent } from 'vue';

import { FilePickerList, FilePreviewDialog } from '@vben/base-ui';

import { getDownloadFileLinkApi, getPreviewFileExternalLink } from '#/api';

export const BaseFilePreviewDialog = defineComponent({
  ...FilePreviewDialog,
  props: {
    ...(FilePreviewDialog as any).props,
    previewApi: {
      type: Function,
      ...(FilePreviewDialog as any).props.previewApi,
      required: false,
      default: getPreviewFileExternalLink,
    },
    downloadApi: {
      type: Function,
      ...(FilePickerList as any).props.downloadApi,
      default: getDownloadFileLinkApi,
    },
  },
});
