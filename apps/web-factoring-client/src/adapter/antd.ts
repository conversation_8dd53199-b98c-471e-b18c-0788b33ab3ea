import type { App, Plugin } from 'vue';

import {
  Button,
  Card,
  Checkbox,
  Col,
  DatePicker,
  Descriptions,
  Form,
  Input,
  InputNumber,
  Radio,
  RadioGroup,
  Row,
  Select,
  Space,
  Tabs,
  Tag,
  TreeSelect,
  Typography,
} from 'ant-design-vue';

// 需要注册的组件列表
const components: Plugin[] = [
  Button,
  Card,
  Checkbox,
  Col,
  DatePicker,
  Descriptions,
  Form,
  Input,
  InputNumber,
  Radio,
  RadioGroup,
  Row,
  Select,
  Space,
  Tabs,
  Tag,
  TreeSelect,
  Typography,
];

export function setupAntd(app: App) {
  components.forEach((component) => {
    app.use(component);
  });
}
