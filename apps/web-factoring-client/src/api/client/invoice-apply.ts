import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface InvoiceApplyInfo {
  /**
   * 不含税金额（元）
   */
  amount?: number;
  /**
   * 价税合计（元）
   */
  amountTax?: number;
  /**
   * 开票申请日期
   */
  applicationDate?: Date;
  /**
   * 附件列表
   */
  attachmentList?: number[];
  /**
   * 购买方名称
   */
  buyerName?: string;
  /**
   * 开票详情列表
   */
  detailList?: InvoiceDetailBO[];
  /**
   * 主键
   */
  id?: number;
  /**
   * 发票代码
   */
  invoiceCode?: string;
  /**
   * 开票日期
   */
  invoiceDate?: Date;
  /**
   * 开票项(开票内容)
   */
  invoiceItem?: string;
  /**
   * 发票号码
   */
  invoiceNumber?: string;
  /**
   * 开票状态
   */
  invoiceStatus?: string;
  /**
   * 开票税率
   */
  invoiceTax?: number;
  /**
   * 发票类型
   */
  invoiceType?: string;
  /**
   * 是否审批
   */
  isReview?: number;
  /**
   * 提交操作
   */
  isSubmit?: boolean;
  /**
   * 项目ID
   */
  projectId?: number;
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 待开票还款记录关系列表
   */
  refList?: InvoiceRefBO[];
  /**
   * 备注
   */
  remarks?: string;
  /**
   * 待开票还款记录编号
   */
  repaymentConfirmCode?: string;
  /**
   * 审批状态
   */
  reviewStatus?: string;
  /**
   * 操作状态
   */
  status?: string;
  /**
   * 税额（元）
   */
  tax?: number;
  /**
   * 税号
   */
  taxNumber?: string;
  [property: string]: any;
}

/**
 * InvoiceDetailBO，开票申请明细
 */
export interface InvoiceDetailBO {
  /**
   * 不含税金额（元）
   */
  amount?: number;
  /**
   * 价税合计（元）
   */
  amountTax?: number;
  /**
   * 主键
   */
  id?: number;
  /**
   * 开票申请ID
   */
  invoiceId?: number;
  /**
   * 服务名称
   */
  invoiceItem?: string;
  /**
   * 数量
   */
  quantity?: number;
  /**
   * 序号
   */
  serialNumber?: number;
  /**
   * 税额（元）
   */
  tax?: number;
  [property: string]: any;
}

/**
 * InvoiceRefBO，开票申请关系
 */
export interface InvoiceRefBO {
  /**
   * 主键
   */
  id?: number;
  /**
   * 开票申请ID
   */
  invoiceId?: number;
  /**
   * 付款记录编号
   */
  paymentConfirmCode?: string;
  /**
   * 付款记录ID
   */
  paymentConfirmId?: number;
  /**
   * 还款记录编号
   */
  repaymentConfirmCode?: string;
  /**
   * 还款记录ID
   */
  repaymentConfirmId?: number;
  [property: string]: any;
}

// 获取分页列表
export async function getInvoiceApplyPageListApi(params: PageListParams) {
  return requestClient.get<InvoiceApplyInfo[]>('/factoring/client/invoice/page', { params });
}
