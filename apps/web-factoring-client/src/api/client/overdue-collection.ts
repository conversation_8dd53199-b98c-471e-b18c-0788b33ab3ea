import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface OverdueCollectionInfo {
  /**
   * 还款单位
   */
  companyName?: string;
  /**
   * 合同号
   */
  contractCode?: string;
  /**
   * 合同名称
   */
  contractName?: string;
  /**
   * 合同签订日期
   */
  contractSignDate?: Date;
  /**
   * 应支付日期
   */
  dueDate?: Date;
  /**
   * 还款计划明细ID
   */
  id?: number;
  /**
   * 逾期天数
   */
  overdueDays?: number;
  /**
   * 催收函文件ID
   */
  overdueFileId?: number;
  /**
   * 催收状态
   */
  overdueStatus?: string;
  /**
   * 期数
   */
  periods?: number;
  /**
   * 项目ID
   */
  projectId?: number;
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 还款计划ID
   */
  repaymentPlanId?: number;
  /**
   * 本息合计（元）
   */
  totalAmount?: number;
  [property: string]: any;
}

// 获取分页列表
export async function getOverdueCollectionListApi(params: PageListParams) {
  return requestClient.get<OverdueCollectionInfo[]>('/factoring/client/warn/overdue/page', { params });
}
