import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface OverviewInfo {
  /**
   * 合同期限（月）
   */
  contractTerm?: number;
  /**
   * 用信审批日期
   */
  creditApplyApprovalDate?: Date;
  /**
   * 用信名称
   */
  creditApplyName?: string;
  /**
   * 决策日期
   */
  creditBeginDate?: Date;
  /**
   * 债权人
   */
  creditorName?: string;
  /**
   * 授信期限（月）
   */
  creditTerm?: number;
  /**
   * 债务人
   */
  debtorName?: string;
  /**
   * 结束日期
   */
  dueDate?: Date;
  /**
   * 业务类型
   */
  factoringType?: string;
  /**
   * 担保人
   */
  guarantorName?: string;
  /**
   * 累计投放金额（元）
   */
  investAmount?: number;
  /**
   * 投放日期
   */
  investDate?: Date;
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 项目类型
   */
  projectType?: string;
  /**
   * 结清日期
   */
  settlementDate?: Date;
  /**
   * 存续状态
   */
  survivalStatus?: string;
  [property: string]: any;
}

// 获取项目信息分页列表
export async function getOverviewPageListApi(params: PageListParams) {
  return requestClient.get<OverviewInfo[]>('/factoring/client/ledger/page', { params });
}
