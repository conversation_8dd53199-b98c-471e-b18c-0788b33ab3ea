<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { ref } from 'vue';

import { FilePreviewDialog } from '@vben/base-ui';
import { Page } from '@vben/common-ui';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  getContractClassifyListApi,
  getContractPageListApi,
  getDownloadFileLinkApi,
  getPreviewFileExternalLink,
} from '#/api';

const dictStore = useDictStore();
// 搜索表单配置
const formOptions = defineFormOptions({
  schema: [
    { component: 'Input', fieldName: 'contractName', label: '合同名称' },
    { component: 'Input', fieldName: 'contractCode', label: '合同编号' },
    {
      component: 'Select',
      fieldName: 'contractCategory',
      label: '合同分类',
      componentProps: {
        api: getContractClassifyListApi,
        labelField: 'categoryName',
        valueField: 'id',
      },
    },
    {
      component: 'Select',
      fieldName: 'sealType',
      label: '用章类型',
      componentProps: {
        options: dictStore.getDictList('SEAL_TYPE'),
      },
    },
  ],
  commonConfig: { labelCol: { span: 6 }, wrapperCol: { span: 18 } },
});

// 表格列配置
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'contractCode', title: '合同编号', minWidth: 200 },
    { field: 'contractName', title: '合同名称', minWidth: 200 },
    {
      field: 'categoryName',
      title: '合同分类',
      minWidth: 150,
    },
    {
      field: 'sealType',
      title: '用章类型',
      minWidth: 150,
      cellRender: { name: 'CellStatus', props: { code: 'SEAL_TYPE' } },
    },
    { field: 'action', title: '操作', fixed: 'right', width: 120, slots: { default: 'action' } },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) =>
        await getContractPageListApi({ current: page.currentPage, size: page.pageSize, ...formValues }),
    },
  },
  checkboxConfig: {
    showHeader: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};

const [Grid] = useVbenVxeGrid({ formOptions, gridOptions });
const FilePreviewDialogRef = ref();
const checkFile = async (file: any, type: string) => {
  if (type === 'Preview') {
    FilePreviewDialogRef.value.init(file.fileId);
  } else {
    const url = await getDownloadFileLinkApi({ id: file.fileId });
    window.open(url);
  }
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="checkFile(row, 'Preview')"> 预览 </a-typography-link>
          <a-typography-link @click="checkFile(row, 'Download')"> 下载 </a-typography-link>
        </a-space>
      </template>
    </Grid>
    <FilePreviewDialog ref="FilePreviewDialogRef" :preview-api="getPreviewFileExternalLink" />
  </Page>
</template>
